<?php

use App\Models\Bill;
use App\Models\BillItem;
use App\Models\BillPayment;
use App\Models\Finance\CashAccount;
use App\Models\Vendor;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    // Create necessary accounts
    CashAccount::factory()->cogs()->create();
    CashAccount::factory()->payable()->create();
    CashAccount::factory()->debitNote()->create();
    CashAccount::factory()->mainCash()->create();
});

it('creates debit note when bill is overpaid', function () {
    // Create a vendor and bill
    $vendor = Vendor::factory()->create();
    $bill = Bill::factory()->create([
        'vendor_id' => $vendor->id,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Create bill items that total 1000
    BillItem::factory()->create([
        'bill_id' => $bill->id,
        'quantity' => 1,
        'unit_price' => 1000,
        'vat' => 0,
    ]);

    // Refresh to recalculate totals
    $bill->refresh();

    // Create an overpayment
    BillPayment::factory()->create([
        'bill_id' => $bill->id,
        'amount' => 1200, // Overpaid by 200
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Refresh the bill to trigger the saved event
    $bill->refresh();

    // Assert that a debit note was created
    $this->assertDatabaseHas('debit_notes', [
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
        'amount' => 200,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Assert that the bill's paid amount was adjusted
    expect($bill->paid)->toBe(1000.0);
});

it('deletes debit note when overpayment is corrected', function () {
    // Create a vendor and bill
    $vendor = Vendor::factory()->create();
    $bill = Bill::factory()->create([
        'vendor_id' => $vendor->id,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Create bill items that total 1000
    BillItem::factory()->create([
        'bill_id' => $bill->id,
        'quantity' => 1,
        'unit_price' => 1000,
        'vat' => 0,
    ]);

    // Refresh to recalculate totals
    $bill->refresh();

    // Create an overpayment
    $payment = BillPayment::factory()->create([
        'bill_id' => $bill->id,
        'amount' => 1200, // Overpaid by 200
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Refresh the bill to trigger the saved event
    $bill->refresh();

    // Assert that a debit note was created
    $this->assertDatabaseHas('debit_notes', [
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
        'amount' => 200,
    ]);

    // Correct the payment amount
    $payment->update(['amount' => 1000]);
    $bill->refresh();

    // Assert that the debit note was deleted
    $this->assertDatabaseMissing('debit_notes', [
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
    ]);
});

it('has correct relationships', function () {
    // Create a vendor and bill
    $vendor = Vendor::factory()->create();
    $bill = Bill::factory()->create([
        'vendor_id' => $vendor->id,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Create bill items that total 1000
    BillItem::factory()->create([
        'bill_id' => $bill->id,
        'quantity' => 1,
        'unit_price' => 1000,
        'vat' => 0,
    ]);

    // Refresh to recalculate totals
    $bill->refresh();

    // Create an overpayment to trigger debit note creation
    BillPayment::factory()->create([
        'bill_id' => $bill->id,
        'amount' => 1100, // Overpaid by 100
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Refresh the bill to trigger the saved event
    $bill->refresh();

    // Get the created debit note
    $debitNote = $bill->debitNote;

    // Test relationships
    expect($debitNote->vendor->id)->toBe($vendor->id);
    expect($debitNote->bill->id)->toBe($bill->id);
    expect($vendor->debitNotes->contains($debitNote))->toBeTrue();
    expect($bill->debitNote->id)->toBe($debitNote->id);
});
