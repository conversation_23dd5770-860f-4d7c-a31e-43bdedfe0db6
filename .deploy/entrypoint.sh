#!/bin/sh

echo "🎬 entrypoint.sh: [$(whoami)] [PHP $(php -r 'echo phpversion();')]"

composer dump-autoload --no-interaction --no-dev --optimize

# Create required directories if not exists
# Need loop for /bin/sh compatibility
for dir in "$LARAVEL_PATH/storage/framework/sessions" "$LARAVEL_PATH/storage/framework/views" "$LARAVEL_PATH/storage/framework/cache"; do
    [ -d "$dir" ] || mkdir -p "$dir"
done
chmod -R 755 "$LARAVEL_PATH/storage/framework"

# Create log file for current date if not exists
log_file="$LARAVEL_PATH/storage/logs/laravel-$(date +%Y-%m-%d).log"
[ -f "$log_file" ] || touch "$log_file"

echo "🎬 artisan commands"

# 💡 Group into a custom command e.g. php artisan app:on-deploy
php artisan storage:link
php artisan migrate --no-interaction --force
if [ "$APP_ENV" = "production" ]; then
    php artisan app:setup-roles
    php artisan app:setup-data
    # php artisan app:fix-data
    # php artisan app:setup-finance-data
fi
php artisan optimize

echo "🎬 start supervisord"

supervisord -c $LARAVEL_PATH/.deploy/config/supervisor.conf
