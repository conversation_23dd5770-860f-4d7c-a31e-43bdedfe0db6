<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 18pt;
            color: #1a2b48;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-row {
            display: table-row;
        }
        .summary-cell {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
        }
        .summary-cell .label {
            font-size: 8pt;
            color: #666;
            margin-bottom: 5px;
        }
        .summary-cell .value {
            font-size: 12pt;
            font-weight: bold;
            color: #1a2b48;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            color: #1a2b48;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 9pt;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .status-paid { color: #059669; }
        .status-unpaid { color: #dc2626; }
        .status-partial { color: #d97706; }
        .status-overdue { color: #dc2626; font-weight: bold; }
        .status-cancelled { color: #6b7280; }
        .aging-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .aging-row {
            display: table-row;
        }
        .aging-cell {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .aging-current { background-color: #d1fae5; }
        .aging-30 { background-color: #fef3c7; }
        .aging-60 { background-color: #fed7aa; }
        .aging-90 { background-color: #fecaca; }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Invoice Report</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['filters']['customer'])
            <p>Customer: {{ $data['filters']['customer'] }}</p>
        @endif
        @if($data['filters']['status'])
            <p>Status: {{ $data['filters']['status'] }}</p>
        @endif
        <p>Currency: {{ $data['currency_code'] ?? 'SAR' }}</p>
    </div>

    {{-- Summary Metrics --}}
    <div class="section">
        <div class="section-title">Summary</div>
        <div class="summary-grid">
            <div class="summary-row">
                <div class="summary-cell">
                    <div class="label">Total Invoices</div>
                    <div class="value">{{ number_format($data['summary']['total_invoices']) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Total Amount</div>
                    <div class="value">{{ money($data['summary']['total_amount'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Total Paid</div>
                    <div class="value">{{ money($data['summary']['total_paid'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Outstanding</div>
                    <div class="value">{{ money($data['summary']['total_outstanding'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-cell">
                    <div class="label">Avg Invoice Value</div>
                    <div class="value">{{ money($data['summary']['average_invoice_value'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Payment Rate</div>
                    <div class="value">{{ number_format($data['summary']['payment_rate'], 1) }}%</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Unique Customers</div>
                    <div class="value">{{ number_format($data['summary']['unique_customers']) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Unique Groups</div>
                    <div class="value">{{ number_format($data['summary']['unique_groups']) }}</div>
                </div>
            </div>
        </div>
    </div>

    {{-- Aging Analysis --}}
    <div class="section">
        <div class="section-title">Aging Analysis</div>
        <div class="aging-grid">
            <div class="aging-row">
                <div class="aging-cell aging-current">
                    <div class="label">Current (0-30 days)</div>
                    <div class="value">{{ money($data['aging_analysis']['current'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="aging-cell aging-30">
                    <div class="label">31-60 days</div>
                    <div class="value">{{ money($data['aging_analysis']['days_31_60'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="aging-cell aging-60">
                    <div class="label">61-90 days</div>
                    <div class="value">{{ money($data['aging_analysis']['days_61_90'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="aging-cell aging-90">
                    <div class="label">Over 90 days</div>
                    <div class="value">{{ money($data['aging_analysis']['over_90'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
            </div>
        </div>
    </div>

    {{-- Grouped Data --}}
    <div class="section">
        <div class="section-title">Invoice Data by {{ ucfirst($data['group_by']) }}</div>
        <table>
            <thead>
                <tr>
                    <th>{{ ucfirst($data['group_by']) }}</th>
                    <th class="text-center">Count</th>
                    <th class="text-right">Amount</th>
                    <th class="text-right">Paid</th>
                    <th class="text-right">Outstanding</th>
                    <th class="text-center">Payment Rate</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['grouped_data'] as $row)
                    <tr>
                        <td>{{ $row['label'] }}</td>
                        <td class="text-center">{{ number_format($row['count']) }}</td>
                        <td class="text-right">{{ money($row['amount'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-right">{{ money($row['paid'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-right">{{ money($row['outstanding'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-center">{{ $row['amount'] > 0 ? number_format(($row['paid'] / $row['amount']) * 100, 1) : 0 }}%</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    {{-- Status Breakdown --}}
    @if(isset($data['summary']['status_counts']) && count($data['summary']['status_counts']) > 0)
        <div class="section">
            <div class="section-title">Status Breakdown</div>
            <table>
                <thead>
                    <tr>
                        <th>Status</th>
                        <th class="text-center">Count</th>
                        <th class="text-center">Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['summary']['status_counts'] as $status => $count)
                        @php
                            $statusEnum = \App\Enums\InvoiceStatus::tryFrom($status);
                            $label = $statusEnum ? $statusEnum->getLabel() : $status;
                            $percentage = $data['summary']['total_invoices'] > 0 ? ($count / $data['summary']['total_invoices']) * 100 : 0;
                        @endphp
                        <tr>
                            <td>{{ $label }}</td>
                            <td class="text-center">{{ number_format($count) }}</td>
                            <td class="text-center">{{ number_format($percentage, 1) }}%</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif

    {{-- Invoice List --}}
    <div class="section page-break">
        <div class="section-title">Invoice List</div>
        <table>
            <thead>
                <tr>
                    <th>Invoice #</th>
                    <th>Date</th>
                    <th>Due Date</th>
                    <th>Customer</th>
                    <th>Group</th>
                    <th class="text-right">Amount</th>
                    <th class="text-right">Paid</th>
                    <th class="text-right">Outstanding</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['invoice_list'] as $invoice)
                    <tr>
                        <td>{{ $invoice['invoice_number'] }}</td>
                        <td>{{ \Carbon\Carbon::parse($invoice['invoice_date'])->format('d M Y') }}</td>
                        <td>{{ \Carbon\Carbon::parse($invoice['due_date'])->format('d M Y') }}</td>
                        <td>{{ $invoice['customer_name'] }}</td>
                        <td>{{ $invoice['group_name'] }}</td>
                        <td class="text-right">{{ money($invoice['amount'], $invoice['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-right">{{ money($invoice['paid'], $invoice['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-right">{{ money($invoice['outstanding'], $invoice['currency_code'] ?? 'SAR', true) }}</td>
                        <td>
                            @php
                                $status = $invoice['status'];
                                $statusEnum = $status instanceof \App\Enums\InvoiceStatus ? $status : \App\Enums\InvoiceStatus::tryFrom($status);
                                $label = $statusEnum ? $statusEnum->getLabel() : $status;
                                $cssClass = match($statusEnum) {
                                    \App\Enums\InvoiceStatus::Paid => 'status-paid',
                                    \App\Enums\InvoiceStatus::Unpaid => 'status-unpaid',
                                    \App\Enums\InvoiceStatus::PaidPartial => 'status-partial',
                                    \App\Enums\InvoiceStatus::Overdue => 'status-overdue',
                                    \App\Enums\InvoiceStatus::Cancelled => 'status-cancelled',
                                    default => ''
                                };
                            @endphp
                            <span class="{{ $cssClass }}">{{ $label }}</span>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div style="position: fixed; bottom: 20px; right: 20px; font-size: 8pt; color: #666;">
        Generated on {{ now()->format('d M Y H:i') }}
    </div>
</body>
</html>
