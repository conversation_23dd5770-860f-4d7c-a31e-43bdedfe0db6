@props([
    'pilgrim',
    'group',
    'fixedSize' => false,
])

@php
    $room = \App\Models\Room::find($pilgrim->pivot->room_id);

    $groupHotelIds = $room?->getAssignedHotelIds() ?? [];
    $groupHotels = $group
        ->group_hotels()
        ->with('hotel')
        ->whereIn('id', $groupHotelIds)
        ->whereHas('hotel', fn ($q) => $q->whereIn('city', ['Makkah', 'Madinah']))
        ->limit(2)
        ->get()
        ->sortByDesc('hotel.city');
@endphp
<div {{ $attributes->merge(['class' => 'bg-white border border-gray-500 border-dashed break-inside-avoid']) }}>
    <div class="{{ $fixedSize ? 'w-[9.5cm] h-[6cm]' : 'w-full' }} flex flex-col">
        <div class="pl-[0.5cm] pr-[0.5cm] pt-[0.3cm] flex-1">
            <div class="flex items-start justify-between">
                <img src="{{ $group->customer->logoUrl ?? asset('images/logo-wide.svg') }}" alt="Logo" class="w-auto h-[1cm]">
                @php
                    $code = base64_encode($group->id . ':' . $pilgrim->id);
                    $url = route('luggage-tag', $code);
                @endphp
                <a href="{{ $url }}" target="_blank">
                    <img class="w-[1.4cm] h-[1.4cm]"
                        src="{{ \App\Services\QRCode::generate($url)->getDataUri() }}">
                </a>
            </div>
            <div class="flex items-start gap-[0.3cm]">
                <div class="w-[2.5cm] h-[3cm] outline outline-1 outline-gray-500 flex-shrink-0 bg-cover bg-center" style="background-image: url('{{ $pilgrim->photoUrl }}')"></div>
                <div class="flex-1 min-w-0 space-y space-y-[0.1cm]">
                    <div class="px-[0.03cm] pb-[0.075cm] border-b border-gray-500 leading-[1]">
                        <p class="text-[5.5pt]">Nama</p>
                        <p class="mt-[1.5pt] font-bold text-[9.5pt] whitespace-nowrap truncate">{{ $pilgrim->fullname }}</p>
                    </div>
                    <div class="px-[0.03cm] pb-[0.075cm] border-b border-gray-500 leading-[1]">
                        <p class="text-[5.5pt]">No. Passport</p>
                        <p class="mt-[1.5pt] font-bold text-[9.5pt]">{{ $pilgrim->passport_number }}</p>
                    </div>
                    @forelse ($groupHotels as $gh)
                        <div class="px-[0.03cm] pb-[0.075cm] border-b border-gray-500 leading-[1] flex justify-between whitespace-nowrap">
                            <div class="min-w-0">
                                <p class="text-[5.5pt]">Hotel {{ $gh->hotel->city }}</p>
                                <p class="mt-[1.5pt] font-bold text-[9.5pt] truncate">{{ $gh->hotel->name }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-[5.5pt]">No. Kamar</p>
                                <p class="mt-[1.5pt] font-bold text-[9.5pt]">{{ $room?->getRoomNumberForHotel($gh->id) }}</p>
                            </div>
                        </div>
                    @empty
                        <div class="px-[0.03cm] pb-[0.075cm] border-b border-gray-500 leading-[1] flex justify-between">
                            <div>
                                <p class="text-[5.5pt]">Hotel Makkah</p>
                                <p class="mt-[1.5pt] font-bold text-[9.5pt]">&nbsp;</p>
                            </div>
                            <div class="text-right">
                                <p class="text-[5.5pt]">No. Kamar</p>
                                <p class="mt-[1.5pt] font-bold text-[9.5pt]">&nbsp;</p>
                            </div>
                        </div>
                        <div class="px-[0.03cm] pb-[0.075cm] border-b border-gray-500 leading-[1] flex justify-between">
                            <div>
                                <p class="text-[5.5pt]">Hotel Madinah</p>
                                <p class="mt-[1.5pt] font-bold text-[9.5pt]">&nbsp;</p>
                            </div>
                            <div class="text-right">
                                <p class="text-[5.5pt]">No. Kamar</p>
                                <p class="mt-[1.5pt] font-bold text-[9.5pt]">&nbsp;</p>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
            <div class="flex items-center h-[1.1cm] justify-between">
                <div class="flex items-center justify-center gap-[0.2cm]">
                    <div class="h-[0.36cm] w-[0.45cm] outline outline-1 outline-gray-500">
                        <div class="h-[0.18cm] bg-red-600"></div>
                    </div>
                    <div class="tracking-[0.15em] font-extrabold text-[9pt]">INDONESIA</div>
                </div>
                <div class="flex items-center text-right leading-[1]">
                    <p class="text-[5.5pt]">No. Roomlist :</p>
                    <div class="w-[1.5cm]">
                        <p class="font-bold text-[19pt]">{{ $room?->number }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-armblue h-[4pt]"></div>
    </div>
</div>
