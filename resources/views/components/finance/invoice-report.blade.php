@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Invoice Report</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['filters']['customer'])
            <p class="text-sm text-gray-600">Customer: {{ $data['filters']['customer'] }}</p>
        @endif
        @if($data['filters']['status'])
            <p class="text-sm text-gray-600">Status: {{ $data['filters']['status'] }}</p>
        @endif
    </div>

    {{-- Summary Metrics --}}
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="p-4 border border-blue-200 rounded-lg bg-blue-50">
            <div class="text-sm font-medium text-blue-600">Total Invoices</div>
            <div class="text-2xl font-bold text-blue-900">{{ number_format($data['summary']['total_invoices']) }}</div>
        </div>
        <div class="p-4 border border-green-200 rounded-lg bg-green-50">
            <div class="text-sm font-medium text-green-600">Total Amount</div>
            <div class="text-2xl font-bold text-green-900">{{ money($data['summary']['total_amount'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-orange-200 rounded-lg bg-orange-50">
            <div class="text-sm font-medium text-orange-600">Total Paid</div>
            <div class="text-2xl font-bold text-orange-900">{{ money($data['summary']['total_paid'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-red-200 rounded-lg bg-red-50">
            <div class="text-sm font-medium text-red-600">Outstanding</div>
            <div class="text-2xl font-bold text-red-900">{{ money($data['summary']['total_outstanding'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
    </div>

    {{-- Additional Summary Stats --}}
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Avg Invoice Value</div>
            <div class="text-xl font-bold text-gray-900">{{ money($data['summary']['average_invoice_value'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-purple-200 rounded-lg bg-purple-50">
            <div class="text-sm font-medium text-purple-600">Payment Rate</div>
            <div class="text-xl font-bold text-purple-900">{{ number_format($data['summary']['payment_rate'], 1) }}%</div>
        </div>
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Unique Customers</div>
            <div class="text-xl font-bold text-gray-900">{{ number_format($data['summary']['unique_customers']) }}</div>
        </div>
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Unique Groups</div>
            <div class="text-xl font-bold text-gray-900">{{ number_format($data['summary']['unique_groups']) }}</div>
        </div>
    </div>

    {{-- Status Breakdown --}}
    @if(isset($data['summary']['status_counts']) && count($data['summary']['status_counts']) > 0)
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">Status Breakdown</h2>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                @foreach($data['summary']['status_counts'] as $status => $count)
                    @php
                        $statusEnum = \App\Enums\InvoiceStatus::tryFrom($status);
                        $color = $statusEnum ? $statusEnum->getColor() : 'gray';
                        $label = $statusEnum ? $statusEnum->getLabel() : $status;
                    @endphp
                    <div class="p-4 border border-{{ $color }}-200 rounded-lg bg-{{ $color }}-50">
                        <div class="text-sm font-medium text-{{ $color }}-600">{{ $label }}</div>
                        <div class="text-xl font-bold text-{{ $color }}-900">{{ number_format($count) }}</div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    {{-- Grouped Data --}}
    <div class="space-y-4">
        <h2 class="text-xl font-semibold">Invoice Data by {{ ucfirst($data['group_by']) }}</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            {{ ucfirst($data['group_by']) }}
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Count
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Amount
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Paid
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Outstanding
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Payment Rate
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($data['grouped_data'] as $row)
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                                {{ $row['label'] }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ number_format($row['count']) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($row['amount'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($row['paid'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($row['outstanding'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ $row['amount'] > 0 ? number_format(($row['paid'] / $row['amount']) * 100, 1) : 0 }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    {{-- Aging Analysis --}}
    <div class="space-y-4">
        <h2 class="text-xl font-semibold">Aging Analysis</h2>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div class="p-4 border border-green-200 rounded-lg bg-green-50">
                <div class="text-sm font-medium text-green-600">Current (0-30 days)</div>
                <div class="text-xl font-bold text-green-900">{{ money($data['aging_analysis']['current'], $data['currency_code'] ?? 'SAR', true) }}</div>
            </div>
            <div class="p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                <div class="text-sm font-medium text-yellow-600">31-60 days</div>
                <div class="text-xl font-bold text-yellow-900">{{ money($data['aging_analysis']['days_31_60'], $data['currency_code'] ?? 'SAR', true) }}</div>
            </div>
            <div class="p-4 border border-orange-200 rounded-lg bg-orange-50">
                <div class="text-sm font-medium text-orange-600">61-90 days</div>
                <div class="text-xl font-bold text-orange-900">{{ money($data['aging_analysis']['days_61_90'], $data['currency_code'] ?? 'SAR', true) }}</div>
            </div>
            <div class="p-4 border border-red-200 rounded-lg bg-red-50">
                <div class="text-sm font-medium text-red-600">Over 90 days</div>
                <div class="text-xl font-bold text-red-900">{{ money($data['aging_analysis']['over_90'], $data['currency_code'] ?? 'SAR', true) }}</div>
            </div>
        </div>
    </div>

    {{-- Recent Invoices --}}
    <div class="space-y-4">
        <h2 class="text-xl font-semibold">Invoice List (Latest 20)</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Invoice #</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Date</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Customer</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Group</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Amount</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Paid</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Outstanding</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach(array_slice($data['invoice_list'], 0, 20) as $invoice)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                                {{ $invoice['invoice_number'] }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ \Carbon\Carbon::parse($invoice['invoice_date'])->format('d M Y') }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ $invoice['customer_name'] }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ $invoice['group_name'] }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($invoice['amount'], $invoice['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($invoice['paid'], $invoice['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($invoice['outstanding'], $invoice['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm whitespace-nowrap">
                                @php
                                    $status = $invoice['status'];
                                    $statusEnum = $status instanceof \App\Enums\InvoiceStatus ? $status : \App\Enums\InvoiceStatus::tryFrom($status);
                                    $color = $statusEnum ? $statusEnum->getColor() : 'gray';
                                    $label = $statusEnum ? $statusEnum->getLabel() : $status;
                                @endphp
                                <span class="inline-flex px-2 text-xs font-semibold leading-5 text-{{ $color }}-800 bg-{{ $color }}-100 rounded-full">
                                    {{ $label }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @if(count($data['invoice_list']) > 20)
            <p class="text-sm text-gray-500 text-center">
                Showing 20 of {{ count($data['invoice_list']) }} invoices. Download the full report for complete data.
            </p>
        @endif
    </div>
</div>
