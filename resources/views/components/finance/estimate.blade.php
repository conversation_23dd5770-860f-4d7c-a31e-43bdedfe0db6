@props([
    'estimate' => null,
    'isInline' => false
])

@if (!$isInline)
<style>
    html {
        color: var(--arm-blue);
        font-size: 10pt;
    }

    #estimate-page .page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    #estimate-page .footer-space {
        height: 1cm;
    }

    @media screen {
        body {
            position: relative;
        }

        #estimate-page .page-footer {
            left: 1cm;
            right: 1cm;
            padding-bottom: 1cm;
            position: absolute;
        }
    }
</style>
@endif
<style>
    :root {
        --arm-blue: #1a2b48;
    }

    #estimate-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }

    #estimate-page .w-full {
        width: 100%;
    }

    #estimate-page .estimate-table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: .5rem 1rem;
    }

    #estimate-page .estimate-table td {
        padding: .5rem 1rem;
        vertical-align: top;
        page-break-inside: avoid;
    }

    #estimate-page .estimate-table tr.bordered td {
        border-bottom: 1px solid #ddd;
    }

    #estimate-page .flex {
        display: flex;
    }

    #estimate-page .flex-col {
        flex-direction: column;
    }

    #estimate-page .grow {
        flex-grow: 1;
    }

    #estimate-page .items-start {
        align-items: flex-start;
    }

    #estimate-page .items-end {
        align-items: flex-end;
    }

    #estimate-page .justify-content-end {
        justify-content: end;
    }

    #estimate-page .text-right {
        text-align: right;
    }

    #estimate-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }

    #estimate-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }

    #estimate-page .muted {
        color: #777;
    }

    #estimate-page .title {
        font-size: 24pt;
    }

    #estimate-page .subtitle {
        color: #666;
        font-weight: bold;
    }

    #estimate-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="estimate-page">
    <table class="w-full">
        <tbody><tr><td>
            <div class="flex items-start w-full">
                <div class="grow">
                    <h1 class="title">PROFORMA INVOICE</h1>
                    <h2 class="subtitle"># {{ $estimate->estimate_number }}</h2>
                </div>
                @php
                    $company = app(App\Settings\CompanySettings::class);
                @endphp
                <div class="flex flex-col items-end text-right grow">
                    @inlinedImage(asset('images/logo-wide.svg'), 'logo')
                    <p><strong>{{ $company->name }}</strong></p>
                    <div class="muted">
                        {!! nl2br($company->address) !!}<br>
                        {{ $company->email }} | {{ $company->phone }}<br>
                        {{ $company->website }}
                    </div>
                </div>
            </div>

            <div class="flex" style="padding: 0.5cm 0;">
                <div style="width: 25%; line-height: 200%;">
                    <p class="muted">Estimate Date :</p>
                    <p class="muted">Valid Until :</p>
                </div>
                <div style="width: 35%; line-height: 200%;">
                    <p>{{ $estimate->estimate_date->format('j M Y') }}</p>
                    <p>{{ $estimate->valid_until->format('j M Y') }}</p>
                </div>
                <div style="width: 40%">
                    <p class="muted">Bill To</p>
                    @if ($estimate->customer)
                        <p><strong>{{ $estimate->customer->name }}</strong></p>
                        @if ($estimate->customer->owner_name || $estimate->customer->phone)
                        <div>
                            {{ $estimate->customer->owner_name }}
                            @if ($estimate->customer->phone)
                            ({{ $estimate->customer->phone }})
                            @endif
                        </div>
                        @endif
                        <div>{{ $estimate->customer->address }}</div>
                    @else
                        <p><strong>[customer dihapus]</strong></p>
                    @endif
                </div>
            </div>

            <div style="padding: 0.5cm 0;">
                <p class="muted">Subject :</p>
                <p>{{ $estimate->subject }}</p>
            </div>

            <table class="w-full estimate-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item & Description</th>
                        <th class="text-right">Qty</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($estimate->items as $item)
                        <tr class="bordered">
                            <td>{{ $loop->index + 1 }}</td>
                            <td>
                                <p>{{ $item->name }}</p>
                                <div class="item-description">{!! nl2br($item->description) !!}</div>
                            </td>
                            <td class="text-right">{{ $item->quantity }}</td>
                            <td class="text-right currency">{{ number_format($item->unit_price, 2, '.', ',') }}</td>
                            <td class="text-right currency">
                                {{ number_format($item->unit_price * $item->quantity, 2, '.', ',') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div class="flex justify-content-end">
                <div style="width: 60%">
                    @php
                        $total = $estimate->total;
                    @endphp
                    <table class="w-full estimate-table">
                        <tbody>
                            <tr>
                                <td class="text-right">Sub Total</td>
                                <td class="text-right currency">{{ money($total, $estimate->currency_code, true) }}</td>
                            </tr>
                            <tr style="background-color: #f2f2f2">
                                <td class="text-right"><strong>Total</strong></td>
                                <td class="text-right currency">
                                    <strong>{{ money($total, $estimate->currency_code, true) }}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="margin-top: 0.75cm;">
                <p class="muted">Notes</p>
                <div style="font-size: 9pt; margin-top: 0.25cm;">{!! nl2br($estimate->notes) !!}</div>
            </div>

            <div style="margin-top: 0.75cm;">
                <p class="muted">Terms & Conditions</p>
                <div style="font-size: 9pt; margin-top: 0.25cm;">{!! nl2br($estimate->terms) !!}</div>
            </div>
        </td></tr></tbody>
        <tfoot><tr><td>
            <div class="footer-space">&nbsp;</div>
        </td></tr></tfoot>
    </table>

    <div class="page-footer" style="margin-top: 0.5cm;">
        <p>Thank you for choosing {{ $company->name }} for hotel reservation and handling service.</p>
    </div>
</div>
