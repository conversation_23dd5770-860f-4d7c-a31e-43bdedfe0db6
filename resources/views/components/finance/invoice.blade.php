@props([
    'invoice' => null,
    'isInline' => false
])

@php
    use App\Enums\InvoiceStatus;
@endphp

@if (!$isInline)
<style>
    html {
        color: var(--arm-blue);
        font-size: 10pt;
    }

    #invoice-page .page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    #invoice-page .footer-space {
        height: 1cm;
    }

    @media screen {
        body {
            position: relative;
        }

        #invoice-page .page-footer {
            left: 1cm;
            right: 1cm;
            padding-bottom: 1cm;
            position: absolute;
        }
    }
</style>
@endif
<style>
    :root {
        --arm-blue: #1a2b48;
    }

    #invoice-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }

    #invoice-page .w-full {
        width: 100%;
    }

    #invoice-page .invoice-table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: .5rem 1rem;
    }

    #invoice-page .invoice-table td {
        padding: .5rem 1rem;
        vertical-align: top;
        page-break-inside: avoid;
    }

    #invoice-page .invoice-table tr.bordered td {
        border-bottom: 1px solid #ddd;
    }

    #invoice-page .flex {
        display: flex;
    }

    #invoice-page .flex-col {
        flex-direction: column;
    }

    #invoice-page .grow {
        flex-grow: 1;
    }

    #invoice-page .items-start {
        align-items: flex-start;
    }

    #invoice-page .items-end {
        align-items: flex-end;
    }

    #invoice-page .justify-content-end {
        justify-content: end;
    }

    #invoice-page .text-right {
        text-align: right;
    }

    #invoice-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }

    #invoice-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }

    #invoice-page .muted {
        color: #777;
    }

    #invoice-page .title {
        font-size: 36pt;
    }

    #invoice-page .subtitle {
        color: #666;
        font-weight: bold;
    }

    #invoice-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="invoice-page">
    <table class="w-full">
        <tbody><tr><td>
            <div class="flex items-start w-full">
                <div class="grow">
                    <h1 class="title">INVOICE</h1>
                    <h2 class="subtitle">Invoice# {{ $invoice->invoice_number }}</h2>

                    <div class="flex items-start" style="margin-top: 0.5cm;">
                    @switch($invoice->status)
                        @case(InvoiceStatus::Paid)
                            <div style="font-weight: bold; color: green; padding: 0.15em 0.65em; border: 2px solid green; font-size: 14pt;">PAID</div>
                            @break
                        @case(InvoiceStatus::PaidPartial)
                            <div style="font-weight: bold; color: orange; padding: 0.15em 0.65em; border: 2px solid orange; font-size: 14pt;">PARTIALLY PAID</div>
                            @break
                        @case(InvoiceStatus::Overdue)
                            <div style="font-weight: bold; color: red; padding: 0.15em 0.65em; border: 2px solid red; font-size: 14pt;">OVERDUE</div>
                            @break
                        @case(InvoiceStatus::Cancelled)
                            <div style="font-weight: bold; color: gray; padding: 0.15em 0.65em; border: 2px solid gray; font-size: 14pt;">CANCELLED</div>
                            @break
                        @default
                            <div style="font-weight: bold; color: orange; padding: 0.15em 0.65em; border: 2px solid orange; font-size: 14pt;">UNPAID</div>
                    @endswitch
                    </div>
                </div>
                @php
                    $company = app(App\Settings\CompanySettings::class);
                @endphp
                <div class="flex flex-col items-end text-right grow">
                    @inlinedImage(asset('images/logo-wide.svg'), 'logo')
                    <p><strong>{{ $company->name }}</strong></p>
                    <div class="muted">
                        {!! nl2br($company->address) !!}<br>
                        {{ $company->email }} | {{ $company->phone }}<br>
                        {{ $company->website }}
                    </div>
                </div>
            </div>

            <div class="flex" style="padding: 0.5cm 0;">
                <div style="width: 25%; line-height: 200%;">
                    <p class="muted">Invoice Date :</p>
                    <p class="muted">Due Date :</p>
                    @if ($invoice->status === InvoiceStatus::Cancelled)
                    <p class="muted">Cancellation Note :</p>
                    @endif
                </div>
                <div style="width: 35%; line-height: 200%;">
                    <p>{{ $invoice->invoice_date->format('j M Y') }}</p>
                    <p>{{ $invoice->due_date->format('j M Y') }}</p>
                    @if ($invoice->status === InvoiceStatus::Cancelled)
                    <p>{{ $invoice->cancellation_note }}</p>
                    @endif
                </div>
                <div style="width: 40%">
                    <p class="muted">Bill To</p>
                    @if ($invoice->customer)
                        <p><strong>{{ $invoice->customer->name }}</strong></p>
                        @if ($invoice->customer->owner_name || $invoice->customer->phone)
                        <div>
                            {{ $invoice->customer->owner_name }}
                            @if ($invoice->customer->phone)
                            ({{ $invoice->customer->phone }})
                            @endif
                        </div>
                        @endif
                        <div>{{ $invoice->customer->address }}</div>
                    @else
                        <p><strong>[customer dihapus]</strong></p>
                    @endif
                </div>
            </div>

            <div style="padding: 0.5cm 0;">
                <p class="muted">Subject :</p>
                <p>{{ $invoice->subject }}</p>
            </div>

            <table class="w-full invoice-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item & Description</th>
                        <th class="text-right">Qty</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($invoice->items as $item)
                        <tr class="bordered">
                            <td>{{ $loop->index + 1 }}</td>
                            <td>
                                <p>{{ $item->name }}</p>
                                <div class="item-description">{!! nl2br($item->description) !!}</div>
                            </td>
                            <td class="text-right">{{ $item->quantity }}</td>
                            <td class="text-right currency">{{ number_format($item->unit_price, 2, '.', ',') }}</td>
                            <td class="text-right currency">
                                {{ number_format($item->unit_price * $item->quantity, 2, '.', ',') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div class="flex justify-content-end">
                <div style="width: 60%">
                    @php
                        $total = $invoice->total;
                        $totalPayment = $invoice->paid;
                        $balanceDue = $invoice->total - $invoice->paid;
                        $invoice->load('creditNote.usedForInvoice');
                    @endphp
                    <table class="w-full invoice-table">
                        <tbody>
                            <tr>
                                <td class="text-right">Sub Total</td>
                                <td class="text-right currency">{{ money($total, $invoice->currency_code, true) }}</td>
                            </tr>
                            <tr>
                                <td class="text-right"><strong>Total</strong></td>
                                <td class="text-right currency">
                                    <strong>{{ money($total, $invoice->currency_code, true) }}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-right"><strong>Total Payments</strong></td>
                                <td class="text-right currency">
                                    <strong>{{ money($totalPayment, $invoice->currency_code, true) }}</strong>
                                </td>
                            </tr>
                            <tr style="background-color: #f2f2f2">
                                <td class="text-right"><strong>Balance Due</strong></td>
                                <td class="text-right currency">
                                    <strong>{{ money($balanceDue, $invoice->currency_code, true) }}</strong>
                                </td>
                            </tr>
                            @if ($invoice->creditNote)
                            <tr>
                                <td class="text-right">
                                    Credit Note
                                    @if ($invoice->creditNote->usedForInvoice)
                                    <div style="font-size: 9pt;">
                                        Used for {{ $invoice->creditNote->usedForInvoice->invoice_number }}
                                    </div>
                                    @endif
                                </td>
                                <td class="text-right currency">
                                    {{ money($invoice->creditNote->amount, $invoice->creditNote->currency_code, true) }}
                                </td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="margin-top: 0.75cm;">
                <p class="muted">Notes</p>
                <div style="font-size: 9pt; margin-top: 0.25cm;">{!! nl2br($invoice->notes) !!}</div>
            </div>

            <div style="margin-top: 0.75cm;">
                <p class="muted">Terms & Conditions</p>
                <div style="font-size: 9pt; margin-top: 0.25cm;">{!! nl2br($invoice->terms) !!}</div>
            </div>
        </td></tr></tbody>
        <tfoot><tr><td>
            <div class="footer-space">&nbsp;</div>
        </td></tr></tfoot>
    </table>

    <div class="page-footer" style="margin-top: 0.5cm;">
        <p>Thank you for choosing {{ $company->name }} for hotel reservation and handling service.</p>
    </div>
</div>
