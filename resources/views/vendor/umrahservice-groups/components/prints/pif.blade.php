@props(['group' => null])

@php
    use Umrahservice\Groups\Enums\GroupStatus;

    if (!$group->relationLoaded('tour_leader')) {
        $group->load([
            'tour_leader',
            'mutawif', 'mutawif_2', 'mutawif_3',
            'flights' => fn ($q) => $q->with(['handler'])->orderBy('date_etd'),
            'group_hotels' => fn ($q) => $q->with(['hotel'])->orderBy('sort')->orderBy('check_in'),
            'manasiks' => fn ($q) => $q->orderBy('sort')->orderBy('date'),
            'itineraries' => fn ($q) => $q->orderBy('sort')->orderBy('date'),
        ]);
    }
    $arrHandlers = $group->flights->where('type', 'arrival')->pluck('handler');
    $depHandlers = $group->flights->where('type', 'departure')->pluck('handler');
@endphp

<section>
    <style>
        /* @page {
            size: 21.5cm 33cm;
            margin: 0;
        } */
        .doc-pif {
            font-size: 9pt;
            position: relative;
        }
        .draft-watermark {
            font-size: 120pt;
            font-weight: bold;
            opacity: 40%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            z-index: 100;
        }
        /* .fi-print-preview .doc-pif {
            padding: 1cm;
        } */
    </style>
    <div class="doc-pif">
        @if ($group->status === GroupStatus::Draft)
            <div class="draft-watermark">DRAFT</div>
        @endif
        <table class="w-full">
            <thead>
                <tr>
                    <td class="pb-[0.5cm]">
                        <div class="hidden print:block print:h-[3.5cm]">&nbsp;</div>
                        <div class="h-[3.5cm] pif-header text-white bg-armblue px-[1cm] pt-[1cm] pb-[0.5cm] flex items-end justify-between print:fixed print:left-0 print:right-0 print:top-0">
                            <div>
                                <img src="{{ asset('images/logo-dark.svg') }}" alt="Logo" class="logo w-auto h-[2cm]" />
                            </div>
                            <div class="px-5 py-1 leading-tight border-l border-white whitespace-nowrap">
                                <h1 class="text-[20pt] font-bold tracking-tight">Package Information Form</h1>
                                <p class="font-bold tracking-tight text-armyellow"><sup class="text-[6pt]">PT.</sup> Arrahmah Umrah Indowisata</p>
                            </div>
                        </div>
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="px-[1cm]">
            <div class="flex gap-1.5 items-start">
                <div class="w-[28%]">
                    <p class="text-[8pt] font-semibold text-gray-500">Customer</p>
                    @if ($group->customer->logo_square || $group->customer->logo)
                        <img src="{{ $group->customer->logoUrl ?? $group->customer->logoSquareUrl }}" alt="Logo Customer" class="w-auto h-[1.5cm] logo mt-3" />
                    @endif
                    <p class="mt-3 font-bold text-[10pt]">{{ $group->customer?->name }}</p>
                </div>
                <div class="w-[20%] space-y-2">
                    @if ($group->tour_leader)
                        <div>
                            <p class="text-[8pt] font-semibold text-gray-500">Tour Leader</p>
                            <p class="font-bold">
                                {{ $group->tour_leader->name }}
                                @if ($group->tour_leader->phone)
                                    <br>{!! whatsapp_link($group->tour_leader->phone) !!}
                                @endif
                            </p>
                        </div>
                    @endif
                    <div>
                        <p class="text-[8pt] font-semibold text-gray-500">Total Pax</p>
                        <p class="font-bold">
                            {{ $group->total_pax }} Pax
                        </p>
                    </div>
                    <div>
                        <p class="text-[8pt] font-semibold text-gray-500">Group</p>
                        <p class="font-bold">
                            {{ $group->name ?? $group->arrival_date?->format('j F Y') ?? '-' }}
                        </p>
                    </div>
                </div>
                <div class="w-[20%] space-y-2">
                    @php
                        $mutawifs = collect([
                            ['name' => $group->muthowwif_name, 'phone' => $group->muthowwif_phone], // deprecated
                            ['name' => $group->mutawif?->name, 'phone' => $group->mutawif?->phone],
                            ['name' => $group->mutawif_2?->name, 'phone' => $group->mutawif_2?->phone],
                            ['name' => $group->mutawif_3?->name, 'phone' => $group->mutawif_3?->phone],
                        ])->filter(fn ($m) => filled($m['name']));
                    @endphp
                    @foreach ($mutawifs as $mutawif)
                        <div>
                            <p class="text-[8pt] font-semibold text-gray-500">Mutawif {{ count($mutawifs) > 1 ? $loop->iteration : '' }}</p>
                            <p class="font-bold">
                                {{ $mutawif['name'] }}
                                @if (filled($mutawif['phone']))
                                    <br>{!! whatsapp_link($mutawif['phone']) !!}
                                @endif
                            </p>
                        </div>
                    @endforeach
                    <div>
                        <p class="text-[8pt] font-semibold text-gray-500">Service</p>
                        <p class="font-bold">
                            {{ $group->getServices() }}
                        </p>
                    </div>
                </div>
                <div class="w-[18%] space-y-2">
                    @if ($group->muassasah)
                        <div>
                            <p class="text-[8pt] font-semibold text-gray-500">Muassasah</p>
                            <p class="font-bold">
                                {!! str($group->muassasah?->company_name ?? '-')->explode(' | ')->join('<br>') !!}
                            </p>
                        </div>
                    @endif
                    @if ($group->meta['use_radiophone'] ?? false)
                        <div>
                            <p class="text-[8pt] font-semibold text-gray-500">Radiophone</p>
                            <p class="font-bold">
                                Yes
                                {{-- {{ $group->radiophone ? 'Yes' : 'No' }} --}}
                            </p>
                        </div>
                    @endif
                </div>
                <div class="w-[14%] space-y-2">
                    @if ($group->muassasah)
                        <div>
                            <p class="text-[8pt] font-semibold text-gray-500">رقم المجموعة</p>
                            <p class="font-bold">
                                {{ $group->number ?? '-' }}
                            </p>
                        </div>
                    @endif
                </div>
            </div>

            @if (filled($group->flights))
                <h3 class="font-bold text-[10pt] relative mt-3 before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                    <span class="relative bg-white pr-[0.5cm]">Flight Information | معلومات الرحلة</span>
                </h3>
                <table class="w-full">
                    <thead class="font-bold text-gray-500">
                        <tr>
                            <th></th>
                            <th class="border border-gray-400">Date</th>
                            <th class="border border-gray-400">Flight No.</th>
                            <th class="border border-gray-400">From</th>
                            <th class="border border-gray-400">To</th>
                            <th class="border border-gray-400">ETD</th>
                            <th class="border border-gray-400">ETA</th>
                            <th class="border border-gray-400">Mealbox</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $arrivals = $group->flights->where('type', 'arrival')->sortBy('date_eta');
                            $arrival_count = $arrivals->reduce(fn (int $carry, $item) => $carry + ($item->via ? 2 : 1), 0);
                            $arrival_count_real = $arrivals->count();
                            $departures = $group->flights->where('type', 'departure')->sortBy('date_etd');
                            $departure_count = $departures->reduce(fn (int $carry, $item) => $carry + ($item->via ? 2 : 1), 0);
                            $departure_count_real = $departures->count();
                        @endphp
                        @if ($arrival_count)
                            @foreach ($arrivals as $arr)
                                @if ($arr->via)
                                <tr>
                                    @if ($loop->iteration == 1)
                                    <td rowspan="{{ $arrival_count }}"
                                        class="font-bold text-center text-white border border-gray-400 bg-armblue">Arrival</td>
                                    @endif
                                    <td class="text-center border border-gray-400" rowspan="2">
                                        {{ $arr->date_etd->format('d M Y') ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->flight_number ?? '-' }}
                                        {{ $arrival_count_real > 1 && $arr->pax ? ' (' . $arr->pax . ' pax)' : '' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->from ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->via ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->etd ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->eta_via ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400" rowspan="2">
                                        {{ collect($arr->mealbox ?? [])->join(', ') }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->via_number ?? '-' }}
                                        {{ $arrival_count_real > 1 && $arr->pax ? ' (' . $arr->pax . ' pax)' : '' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->via ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->to ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->etd_via ?? '-' }}
                                    </td>
                                    <td class="text-center text-white border border-gray-400 bg-armblue">
                                        {{ $arr->eta ?? '-' }}
                                    </td>
                                </tr>
                                @else
                                <tr>
                                    @if ($loop->iteration == 1)
                                    <td rowspan="{{ $arrival_count }}"
                                        class="font-bold text-center text-white border border-gray-400 bg-armblue">Arrival</td>
                                    @endif
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->date_etd->format('d M Y') ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->flight_number ?? '-' }}
                                        {{ $arrival_count_real > 1 && $arr->pax ? ' (' . $arr->pax . ' pax)' : '' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->from ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->to ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $arr->etd ?? '-' }}
                                    </td>
                                    <td class="text-center text-white border border-gray-400 bg-armblue">
                                        {{ $arr->eta ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ collect($arr->mealbox ?? [])->join(', ') }}
                                    </td>
                                </tr>
                                @endif
                            @endforeach
                        @endif
                        @if ($departure_count)
                            @foreach ($departures as $dep)
                                @if ($dep->via)
                                <tr>
                                    @if ($loop->iteration == 1)
                                        <td rowspan="{{ $departure_count }}"
                                            class="font-bold text-center text-white border border-gray-400 bg-armblue">Departure</td>
                                    @endif
                                    <td class="text-center border border-gray-400" rowspan="2">
                                        {{ $dep->date_etd->format('d M Y') ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->flight_number ?? '-' }}
                                        {{ $departure_count_real > 1 && $dep->pax ? ' (' . $dep->pax . ' pax)' : '' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->from ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->via ?? '-' }}
                                    </td>
                                    <td class="text-center text-white border border-gray-400 bg-armblue">
                                        {{ $dep->etd ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->eta_via ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400" rowspan="2">
                                        {{ collect($dep->mealbox ?? [])->join(', ') }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->via_number ?? '-' }}
                                        {{ $departure_count_real > 1 && $dep->pax ? ' (' . $dep->pax . ' pax)' : '' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->via ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->to ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->etd_via ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->eta ?? '-' }}
                                    </td>
                                </tr>
                                @else
                                <tr>
                                    @if ($loop->iteration == 1)
                                        <td rowspan="{{ $departure_count }}"
                                            class="font-bold text-center text-white border border-gray-400 bg-armblue">Departure</td>
                                    @endif
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->date_etd->format('d M Y') ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->flight_number ?? '-' }}
                                        {{ $departure_count_real > 1 && $dep->pax ? ' (' . $dep->pax . ' pax)' : '' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->from ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->to ?? '-' }}
                                    </td>
                                    <td class="text-center text-white border border-gray-400 bg-armblue">
                                        {{ $dep->etd ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $dep->eta ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ collect($dep->mealbox ?? [])->join(', ') }}
                                    </td>
                                </tr>
                                @endif
                            @endforeach
                        @endif
                    </tbody>
                </table>
            @endif

            @if (filled($group->group_hotels))
                <h3 class="font-bold text-[10pt] relative mt-3 before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                    <span class="relative bg-white pr-[0.5cm]">Hotels Information | معلومات التسكين</span>
                </h3>
                <table class="w-full">
                    <thead class="text-white bg-armblue">
                        <tr>
                            <th rowspan="2"
                                class="border border-gray-400">City</th>
                            <th rowspan="2"
                                class="border border-gray-400">Hotel</th>
                            <th colspan="2" class="border border-gray-400">Date
                            </th>
                            <th rowspan="2" class="border border-gray-400">Total<br>
                                Days</th>
                            <th colspan="6" class="border border-gray-400">
                                Composition of Room</th>
                            <th rowspan="2"
                                class="border border-gray-400">Type</th>
                            <th rowspan="2"
                                class="border border-gray-400">Meal</th>
                        </tr>
                        <tr>
                            <th class="border border-gray-400">
                                In</th>
                            <th class="border border-gray-400">
                                Out</th>
                            <th class="border border-gray-400">S</th>
                            <th class="border border-gray-400">D</th>
                            <th class="border border-gray-400">T</th>
                            <th class="border border-gray-400">Q</th>
                            <th class="border border-gray-400">QT</th>
                            <th class="border border-gray-400 text-[8pt]">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($group->group_hotels as $hotel)
                            <tr>
                                <td class="px-2 text-center border border-gray-400">
                                    {{ $hotel->hotel->city }}</td>
                                <td class="px-2 text-center border border-gray-400">
                                    {{ $hotel->hotel->name }}</td>
                                <td class="text-center border border-gray-400">
                                    {{ $hotel->check_in->format('d M Y') }}</td>
                                <td class="text-center border border-gray-400">
                                    {{ $hotel->check_out->format('d M Y') }}</td>
                                <td class="text-center border border-gray-400">
                                    {{ $hotel->check_in->diffInDays($hotel->check_out) }}</td>
                                <td class="text-center border border-gray-400">{{ $hotel->room_single_count }}</td>
                                <td class="text-center border border-gray-400">{{ $hotel->room_double_count }}</td>
                                <td class="text-center border border-gray-400">{{ $hotel->room_triple_count }}</td>
                                <td class="text-center border border-gray-400">{{ $hotel->room_quad_count }}</td>
                                <td class="text-center border border-gray-400">{{ $hotel->room_quint_count }}</td>
                                <td class="text-center border border-gray-400">
                                    {{ $hotel->room_double_count + $hotel->room_triple_count + $hotel->room_quad_count + $hotel->room_quint_count }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    @php $roomType = App\Enums\HotelRoomType::tryFrom($hotel->meta['room_type'] ?? ''); @endphp
                                    {{ $roomType?->getShortLabel() }}
                                </td>
                                <td class="text-center border border-gray-400">{{ $hotel->meta['meal'] ?? null }}</td>
                            </tr>
                        @endforeach
                        @if ($group->meta['room_service'] ?? null)
                            <tr>
                                <th colspan="2" class="text-white border border-gray-400 bg-armblue">Room Service</th>
                                <td colspan="11" class="px-2 italic border border-gray-400">{{ $group->meta['room_service'] }}</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            @endif

            @if (filled($group->manasiks))
                <h3 class="font-bold text-[10pt] relative mt-3 before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                    <span class="relative bg-white pr-[0.5cm]">Manasik | مناسك</span>
                </h3>
                <table class="w-full">
                    <thead class="text-white bg-armblue">
                        <tr>
                            <th class="border border-gray-400">Manasik</th>
                            <th class="border border-gray-400">Date</th>
                            <th class="border border-gray-400">Time</th>
                            <th class="border border-gray-400">Additional Facilities</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($group->manasiks as $manasik)
                            <tr>
                                <td class="text-center border border-gray-400">
                                    {{ $manasik->name }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    {{ $manasik->date->format('d M Y') }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    {{ $manasik->date->format('H:i') }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    {{ $manasik->addons }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @endif

            @if ($group->transport_id)
                <h3 class="font-bold text-[10pt] relative mt-3 before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                    <span class="relative bg-white pr-[0.5cm]">Transportation Details | معلومات النقل</span>
                </h3>
                <table class="w-full">
                    <tbody>
                        <tr>
                            <td rowspan="{{ 1 + (filled($group->group_vehicles) ? count($group->group_vehicles) : 1) }}"
                                class="font-bold text-center text-white border border-gray-400 bg-armblue">Bus
                            </td>
                            @if (filled($group->group_vehicles))
                                <td class="font-bold text-center border border-gray-400">Vehicle</td>
                                <td class="font-bold text-center border border-gray-400">Capacity</td>
                                <td class="font-bold text-center border border-gray-400">Total Pax</td>
                            @else
                                <td class="font-bold text-center border border-gray-400">Total Bus</td>
                                <td class="font-bold text-center border border-gray-400">Total Pax</td>
                                <td class="font-bold text-center border border-gray-400">Max Per Bus</td>
                            @endif
                            <td colspan="2" class="font-bold text-center text-white border border-gray-400 bg-armblue">
                                Company Name | شركة النقل</td>
                        </tr>
                        @if (filled($group->group_vehicles))
                            @foreach ($group->group_vehicles as $gv)
                                <tr>
                                    <td class="text-center border border-gray-400">
                                        {{ $gv->count . ' x ' . $gv->vehicle->name }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ $gv->vehicle->capacity }} Pax</td>
                                    <td class="text-center border border-gray-400">
                                        {{ $gv->pax_count }} Pax</td>
                                    @if ($loop->index == 0)
                                        <td colspan="2" rowspan="{{ count($group->group_vehicles) }}"
                                            class="font-bold text-center border border-gray-400">
                                            {{ $group->transport?->company_name ?? '-' }}
                                        </td>
                                    @endif
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td class="text-center border border-gray-400">
                                    {{ $group->transport_bus_count ?? 0 }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    {{ $group->total_pax }} Pax</td>
                                <td class="text-center border border-gray-400">48 Pax</td>
                                <td colspan="2" class="font-bold text-center border border-gray-400">
                                    {{ $group->transport?->company_name ?? '-' }}
                                </td>
                            </tr>
                        @endif
                        @if ($group->hasService('train'))
                            @php
                                $lines = $group->meta['trains'] ?? [];
                            @endphp
                            <tr>
                                <td rowspan="{{ count($lines) + 1 }}"
                                    class="font-bold text-center text-white border border-gray-400 bg-armblue">Trains
                                </td>
                                <td colspan="3" class="font-bold text-center border border-gray-400">Train Line</td>
                                <td class="font-bold text-center text-white border border-gray-400 bg-armblue">
                                    Train ETD</td>
                                <td class="font-bold text-center text-white border border-gray-400 bg-armblue">
                                    Train ETA</td>
                            </tr>
                            @foreach ($lines as $line)
                                <tr>
                                    <td colspan="3" class="text-center border border-gray-400">
                                        {{ $line['line'] }}</td>
                                    <td class="text-center border border-gray-400">
                                        {{ \Carbon\Carbon::parse($line['etd'])->format('d M Y H:i') }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {{ \Carbon\Carbon::parse($line['eta'])->format('d M Y H:i') }}
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            @endif

            @if (filled($group->itineraries))
                <table class="w-full mt-3">
                    <thead>
                        <tr>
                            <th colspan="5" class="text-white border border-gray-400 bg-armblue">
                                Transportation Schedule | معلومات النقل
                            </th>
                        </tr>
                        <tr class="text-gray-500">
                            <th class="px-2 border border-gray-400">No.</th>
                            <th class="px-2 border border-gray-400">Date</th>
                            <th class="px-2 border border-gray-400">Time</th>
                            <th class="px-2 border border-gray-400">Trips</th>
                            <th class="px-2 border border-gray-400">Information</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($group->itineraries as $itinerary)
                            <tr class="border border-gray-400 {{ $loop->index % 2 == 0 ? 'bg-gray-200' : '' }}">
                                <td class="px-2 text-center border border-gray-400">
                                    {{ $loop->index + 1 }}
                                </td>
                                <td class="px-2 text-center border border-gray-400 whitespace-nowrap">
                                    {{ $itinerary->date->format('d M Y') }}
                                </td>
                                <td class="px-2 text-center border border-gray-400">
                                    {{ $itinerary->date->format('H:i') }}
                                </td>
                                <td class="px-2 border border-gray-400">
                                    {{ $itinerary->location }}
                                </td>
                                <td class="px-2 border border-gray-400">
                                    {{ $itinerary->description }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @endif

            @if (filled($group->meals))
            <div class="mt-3 break-inside-avoid">
                <h3 class="font-bold text-[10pt] relative before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                    <span class="relative bg-white pr-[0.5cm]">Meals Information</span>
                </h3>
                <table class="w-full">
                    <thead class="bg-armblue">
                        <tr>
                            <th class="w-1/3 px-2 text-white border border-gray-400">Arrival Meals</th>
                            <th class="w-1/3 px-2 text-white border border-gray-400">Departure Meals</th>
                            <th class="w-1/3 px-2 text-white border border-gray-400">Extra Meals</th>
                        </tr>
                    </thead>
                    <tbody class="text-center">
                        <tr>
                            <td class="px-2 border border-gray-400">{{ $group->meals['arrival'] ?? '-' }}</td>
                            <td class="px-2 border border-gray-400">{{ $group->meals['departure'] ?? '-' }}</td>
                            <td class="px-2 border border-gray-400">{{ $group->meals['extra'] ?? '-' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            @endif

            <div class="mt-3 break-inside-avoid">
                <h3 class="font-bold text-[10pt] relative before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                    <span class="relative bg-white pr-[0.5cm]">PIC Information</span>
                </h3>
                <table class="w-full">
                    <thead class="bg-armblue">
                        <tr>
                            @if ($group->hasService('handling') || $group->hasService('airport'))
                                <th colspan="3" class="w-1/2 text-white border border-gray-400">
                                    Airport Handling Contact
                                </th>
                            @else
                                <th colspan="3" class="w-1/2"></th>
                            @endif
                            @if ($group->hotel_handlers && sizeof($group->hotel_handlers) > 0)
                                <th colspan="3" class="w-1/2 text-white border border-gray-400">
                                    Handling Team Contact
                                </th>
                            @else
                                <th colspan="3" class="w-1/2"></th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $handlers_count = $group->hotel_handlers ? sizeof($group->hotel_handlers) : 0;
                        @endphp
                        <tr>
                            @if ($group->hasService('handling') || $group->hasService('airport'))
                                <th class="text-white border border-gray-400 bg-armblue">
                                    Arrival
                                </th>
                                <td class="text-center border border-gray-400">
                                    @foreach ($arrHandlers as $a)
                                        <p>{{ $a?->company_name ?? '' }}</p>
                                    @endforeach
                                </td>
                                <td class="text-center border border-gray-400">
                                    @foreach ($arrHandlers as $a)
                                        <p>{!! whatsapp_link($a->contact_phone ?? '-') !!}</p>
                                    @endforeach
                                </td>
                            @else
                                <td colspan="3"></td>
                            @endif
                            @if (isset($group->hotel_handlers[0]))
                                <th class="text-white border border-gray-400 bg-armblue">
                                    {{ $group->hotel_handlers[0]['city'] ?? '-' }} Team
                                </th>
                                <td class="text-center border border-gray-400">
                                    {{ $group->hotel_handlers[0]['name'] ?? '-' }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    {!! whatsapp_link($group->hotel_handlers[0]['phone'] ?? '-') !!}
                                </td>
                            @endif
                        </tr>
                        <tr>
                            @if ($group->hasService('handling') || $group->hasService('airport'))
                                <th class="text-white border border-gray-400 bg-armblue">
                                    Departure
                                </th>
                                <td class="text-center border border-gray-400">
                                    @foreach ($depHandlers as $a)
                                        <p>{{ $a?->company_name ?? '' }}</p>
                                    @endforeach
                                </td>
                                <td class="text-center border border-gray-400">
                                    @foreach ($depHandlers as $a)
                                        <p>{!! whatsapp_link($a->contact_phone ?? '-') !!}</p>
                                    @endforeach
                                </td>
                            @else
                                <td colspan="3"></td>
                            @endif
                            @if (isset($group->hotel_handlers[1]))
                                <th class="text-white border border-gray-400 bg-armblue">
                                    {{ $group->hotel_handlers[1]['city'] ?? '-' }} Team
                                </th>
                                <td class="text-center border border-gray-400">
                                    {{ $group->hotel_handlers[1]['name'] ?? '-' }}
                                </td>
                                <td class="text-center border border-gray-400">
                                    {!! whatsapp_link($group->hotel_handlers[1]['phone'] ?? '-') !!}
                                </td>
                            @endif
                        </tr>
                        @if ($handlers_count > 2)
                            @for ($i = 2; $i < $handlers_count; $i++)
                                <tr>
                                    <td colspan="3"></td>
                                    <th class="text-white border border-gray-400 bg-armblue">
                                        {{ $group->hotel_handlers[$i]['city'] ?? '-' }} Team
                                    </th>
                                    <td class="text-center border border-gray-400">
                                        {{ $group->hotel_handlers[$i]['name'] ?? '-' }}
                                    </td>
                                    <td class="text-center border border-gray-400">
                                        {!! whatsapp_link($group->hotel_handlers[$i]['phone'] ?? '-') !!}
                                    </td>
                                </tr>
                            @endfor
                        @endif
                    </tbody>
                </table>
            </div>

            @php $notes = collect($group->meta['notes'] ?? [])->filter()->values(); @endphp
            @if (filled($notes))
                <div class="mt-3 break-inside-avoid">
                    <h3 class="font-bold text-[10pt] relative mt-3 before:content-normal before:h-[2px] before:bg-armyellow before:absolute before:left-0 before:right-0 before:top-2">
                        <span class="relative bg-white pr-[0.5cm]">Notes</span>
                    </h3>
                    <table class="w-full">
                        <tbody>
                            <tr>
                                <th class="w-1/6 text-white border border-gray-400 bg-armblue">
                                    Service Notes
                                </th>
                                <td class="px-2 py-1 border border-gray-400">
                                    <ul class="columns-2">
                                        @foreach ($notes as $note)
                                            <li class="flex gap-1.5 items-start break-inside-avoid-column">
                                                <span>&bull;</span>
                                                <span>{{ $note }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </td>
                        </tbody>
                    </table>
                </div>
            @endif
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td class="pt-[0.5cm]">
                        <div class="hidden print:block print:h-[2cm]">&nbsp;</div>
                        <div class="pif-footer bg-armblue text-white h-[2cm] flex items-center px-[1cm] border-t-4 border-armyellow justify-between print:fixed print:left-0 print:right-0 print:bottom-0">
                            <div class="flex flex-col items-start font-bold leading-3 tracking-tight">
                                <span>Pelopor Layanan</span>
                                <span class="text-armyellow">Land Arrangement</span>
                                <span>Umrah &amp; Haji</span>
                            </div>
                            <div class="italic">
                                Thank you for choosing Arrahmah Handling Service as your trusted partner for handling services.
                            </div>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</section>
