<x-filament-panels::page>
    @if($record->attachments && count($record->attachments) > 0)
    <div class="mb-4">
        <div class="bg-white rounded-lg border border-gray-200 p-3">
            <h3 class="text-sm font-medium text-gray-900 mb-2">Attachments</h3>
            <div class="flex flex-wrap gap-2">
                @foreach($record->attachments as $attachment)
                    <div class="flex items-center space-x-2 px-2 py-1 bg-gray-50 rounded text-sm">
                        <x-heroicon-o-document class="w-4 h-4 text-gray-400" />
                        <a href="{{ \Illuminate\Support\Facades\Storage::disk('s3')->url($attachment) }}"
                           target="_blank"
                           class="text-primary-600 hover:text-primary-500 font-medium">
                            {{ basename($attachment) }}
                        </a>
                        <a href="{{ \Illuminate\Support\Facades\Storage::disk('s3')->url($attachment) }}"
                           download
                           class="text-gray-400 hover:text-gray-600">
                            <x-heroicon-o-arrow-down-tray class="w-3 h-3" />
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <div class="overflow-x-auto">
        <div class="mx-auto bg-white rounded-xl shadow" style="width: 21cm; padding: 1cm; font-size: 10pt;" id="bill-print">
            <x-finance.bill :record="$record" is-inline />
        </div>
    </div>
</x-filament-panels::page>
