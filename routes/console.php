<?php

use Illuminate\Support\Facades\Schedule;

Schedule::command('app:update-group-progress')->hourly();
Schedule::command('app:fix-data')->hourly();
Schedule::command('backup:clean')->daily()->at('00:00');
Schedule::command('backup:run --only-db')->daily()->at('00:30');
Schedule::command('backup:run --only-files')->lastDayOfMonth('00:30');
Schedule::command('activitylog:clean')->weekly();

Schedule::command('estimates:check-expired')->dailyAt('00:00');
Schedule::command('invoices:check-overdue')->dailyAt('00:00');

Schedule::command('reminders:schedule')->hourly();
Schedule::command('reminders:send')->everyFiveMinutes();

Schedule::command('exchange-rates:refresh')->dailyAt('03:30'); // 08.30 jakarta time

Schedule::command('exchange:update SAR --days=1')->dailyAt('03:30'); // 08.30 jakarta time
Schedule::command('exchange:update USD --days=1')->dailyAt('03:30'); // 08.30 jakarta time
