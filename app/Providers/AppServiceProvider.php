<?php

namespace App\Providers;

use App\Contracts\CurrencyHandler;
use App\Contracts\PdfService;
use App\Contracts\WhatsAppService;
use App\Http\Middleware\SetPeriod;
use App\Models;
use App\Models\Customer;
use App\Services\BankIndonesiaKursService;
use App\Services\BankIndonesiaKursServiceV2;
use App\Services\CurrencyService;
use App\Services\GotenbergPdfService;
use App\Services\LaravelPdfService;
use App\Services\ReminderService;
use App\Services\WanotifWhatsAppService;
use App\Services\WuzapiWhatsAppService;
use App\Settings\WhatsAppSettings;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use BezhanSalleh\PanelSwitch\PanelSwitch;
use Filament\FontProviders\GoogleFontProvider;
use Filament\Panel;
use Filament\Support\Assets\Js;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Facades\FilamentAsset;
use Filament\Support\Facades\FilamentView;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\IconColumn\IconColumnSize;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\View\PanelsRenderHook;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(CurrencyHandler::class, function (Application $app) {
            $driver = config('services.currency.driver');

            return match ($driver) {
                'bank_indonesia' => new BankIndonesiaKursService,
                'bank_indonesia_v2' => new BankIndonesiaKursServiceV2,
                'exchange_rates_api' => new CurrencyService(
                    config('services.currency.api_key'),
                    config('services.currency.base_url'),
                    $app->make(Client::class)
                ),
                default => throw new \InvalidArgumentException('Invalid currency driver'),
            };
        });

        $this->app->bind(WhatsAppService::class, function (Application $app) {
            $driver = config('services.whatsapp.driver');
            $settings = $app->make(WhatsAppSettings::class)->toArray();

            return match ($driver) {
                'wanotif' => new WanotifWhatsAppService($settings['api_url'], $settings['user_token'], $settings['device_id']),
                'wuzapi' => new WuzapiWhatsAppService($settings['api_url'], $settings['user_token']),
                default => throw new \InvalidArgumentException('Invalid WhatsApp driver'),
            };
        });

        $this->app->bind(PdfService::class, function () {
            $driver = config('services.pdf.driver');

            return match ($driver) {
                'laravel-pdf' => new LaravelPdfService,
                'gotenberg' => new GotenbergPdfService,
                default => throw new \InvalidArgumentException('Invalid PDF driver'),
            };
        });

        $this->app->singleton(ReminderService::class);

        Panel::configureUsing(function (Panel $panel) {
            $panel
                ->darkMode(false)
                ->brandLogo(asset('images/logo-wide.svg'))
                ->brandLogoHeight('2.5rem')
                ->favicon(asset('images/icon.png'))
                ->font('Plus Jakarta Sans', provider: GoogleFontProvider::class)
                ->viteTheme('resources/css/filament/admin/theme.css')
                ->sidebarCollapsibleOnDesktop()
                ->sidebarWidth('18rem')
                ->databaseNotifications();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (app()->isProduction()) {
            URL::forceScheme('https');
        }

        // Model::shouldBeStrict(! app()->isProduction());
        DB::prohibitDestructiveCommands(app()->isProduction());

        Route::bind('customerUuid', function ($value) {
            return Customer::where('uuid', $value)->first();
        });

        FilamentAsset::register([
            Js::make('plugins', Vite::asset('resources/js/plugins.js'))->module(),
        ]);

        FilamentView::registerRenderHook('panels::global-search.before', fn () => Blade::render("@livewire('period-picker')"));
        FilamentView::registerRenderHook('panels::sidebar.nav.start', fn () => Blade::render("@livewire('period-picker-sidebar')"));
        FilamentView::registerRenderHook('panels::auth.login.form.after', fn () => Blade::render('<x-filament::button href="https://travel.umrahservice.co" tag="a" outlined icon="heroicon-m-arrow-top-right-on-square" icon-position="after">Customer Login</x-filament::button>'));
        FilamentView::registerRenderHook(PanelsRenderHook::BODY_END, fn () => <<<'HTML'
            <script>
                document.addEventListener('livewire:init', () => {
                    Livewire.hook('commit', ({ component, commit, respond, succeed, fail }) => {
                        // Runs immediately before a commit's payload is sent to the server...

                        respond(() => {
                            // Runs after a response is received but before it's processed...
                        })

                        succeed(({ snapshot, effect }) => {
                            // Runs after a successful response is received and processed
                            // with a new snapshot and list of effects...
                            setTimeout(() => {
                                window.refreshFsLightbox();
                            }, 500);
                        })

                        fail(() => {
                            // Runs if some part of the request failed...
                        })
                    })
                })
            </script>
        HTML);

        Relation::enforceMorphMap([
            'user' => \App\Models\User::class,
            'user_cash' => Models\Finance\UserCash::class,
            'customer' => \App\Models\Customer::class,
            'customer_user' => \App\Models\CustomerUser::class,
            'customer_cash' => Models\Finance\CustomerCash::class,
            'group' => \App\Models\Group::class,
            'hotel' => \App\Models\Hotel::class,
            'group_hotel' => \App\Models\GroupHotel::class,
            'group_data' => \App\Models\GroupData::class,
            'group_cash' => \App\Models\GroupCash::class,
            'group_flight' => \App\Models\GroupFlight::class,
            'itinerary' => \App\Models\Itinerary::class,
            'bill' => Models\Bill::class,
            'bill_item' => Models\BillItem::class,
            'bill_payment' => Models\BillPayment::class,
            'debit_note' => Models\Finance\DebitNote::class,
            'purchase_order' => Models\Finance\PurchaseOrder::class,
            'purchase_order_item' => Models\Finance\PurchaseOrderItem::class,
            'payment_voucher' => Models\Finance\PaymentVoucher::class,
            'product' => \App\Models\Finance\Product::class,
            'invoice' => \App\Models\Finance\Invoice::class,
            'invoice_payment' => \App\Models\Finance\InvoicePayment::class,
            'invoice_refund' => \App\Models\Finance\InvoiceRefund::class,
            'institution' => \App\Models\Institution::class,
            'transport' => \App\Models\Transport::class,
            'hotel_broker' => \App\Models\Vendors\HotelBroker::class,
            'activity' => \Spatie\Activitylog\Models\Activity::class,
            'balance' => Models\Finance\Balance::class,
            'cash_transaction' => Models\Finance\CashTransaction::class,
            'credit_note' => Models\Finance\CreditNote::class,
        ]);

        Livewire::addPersistentMiddleware([
            SetPeriod::class,
        ]);

        PanelSwitch::configureUsing(function (PanelSwitch $panelSwitch) {
            $panelSwitch
                ->simple()
                ->visible(fn () => auth()->user()?->hasAnyRole(['Admin', 'Finance']))
                ->excludes(['booking'])
                ->renderHook('panels::global-search.after');
        });

        BadgeableColumn::configureUsing(function (BadgeableColumn $badgeableColumn) {
            $badgeableColumn
                ->asPills();
        });

        Table::configureUsing(fn (Table $table) => $table
            ->emptyStateHeading('No data')
            ->paginated([10, 25, 50, 100]));

        // DateRangeFilter::configureUsing(fn (DateRangeFilter $dateRangeFilter) => $dateRangeFilter
        //     ->indicateUsing(function ($component, $data) {
        //         $datesString = data_get($data, $component->column);

        //         if (empty($datesString)) {
        //             return null;
        //         }

        //         return $component->getLabel().": $datesString";
        //     }));

        PhoneInput::configureUsing(function (PhoneInput $phoneInput) {
            $phoneInput
                ->defaultCountry('ID');
        });
        // FIXME: This is not working
        // PhoneColumn::configureUsing(function (PhoneColumn $phoneColumn) {
        //     $phoneColumn
        //         ->displayFormat(PhoneInputNumberType::INTERNATIONAL);
        // });

        TextColumn::macro('tabularNums', function () {
            /** @var TextColumn $this */
            $this->extraAttributes([
                'class' => 'tabular-nums',
            ]);

            return $this;
        });
        TextColumn::macro('currencyRight', function () {
            /** @var TextColumn $this */
            return $this
                ->currency('SAR', true)
                ->alignRight()
                ->weight(FontWeight::Medium)
                ->extraAttributes([
                    'class' => 'tabular-nums',
                ]);
        });
        TextColumn::macro('currencyAuto', function () {
            /** @var TextColumn $this */
            return $this
                ->formatStateUsing(function ($record, $state) {
                    $currency = $record->currency_code ?? 'SAR';

                    return money(floatval($state), $currency, true);
                })
                ->alignRight()
                ->weight(FontWeight::Medium)
                ->description(function ($record, $state) {
                    if ($record->currency_code === 'SAR') {
                        return null;
                    }

                    return money($state * $record->exchange_rate, 'SAR', true);
                })
                ->extraAttributes([
                    'class' => 'tabular-nums',
                ]);
        });

        IconColumn::macro('attachment', function () {
            /** @var IconColumn $this */
            return $this
                ->label('Att.')
                ->url(fn ($record) => $record->attachment
                    ? (str_starts_with($record->attachment, 'http')
                        ? $record->attachment
                        : config('filesystems.disks.s3.url') . $record->attachment)
                    : null)
                ->openUrlInNewTab()
                ->icon(fn ($state) => $state ? match (str($state)->afterLast('.')->toString()) {
                    'pdf' => 'phosphor-file-pdf',
                    'jpg' => 'phosphor-file-jpg',
                    'png' => 'phosphor-file-png',
                    default => 'phosphor-file-text'
                } : null)
                ->alignCenter()
                ->color('primary')
                ->size(IconColumnSize::ExtraLarge)
                ->extraAttributes(function ($record) {
                    if ($record->attachment) {
                        $url = str_starts_with($record->attachment, 'http')
                            ? $record->attachment
                            : config('filesystems.disks.s3.url') . $record->attachment;

                        return [
                            'x-on:click' => "SimpleLightBox.open(event, '{$url}')",
                            'src' => $url,
                        ];
                    }

                    return [];
                }, true);
        });

        IconColumn::macro('attachments', function () {
            /** @var IconColumn $this */
            return $this
                ->label('Att.')
                ->getStateUsing(fn ($record) => filled($record->attachments) ? $record->attachments[0] : null)
                ->url(fn ($record) => filled($record->attachments)
                    ? (str_starts_with($record->attachments[0], 'http')
                        ? $record->attachments[0]
                        : config('filesystems.disks.s3.url') . $record->attachments[0])
                    : null)
                ->openUrlInNewTab()
                ->icon(fn ($record) => filled($record->attachments)
                ? match (str($record->attachments[0])->afterLast('.')->toString()) {
                    'pdf' => 'phosphor-file-pdf',
                    'jpg' => 'phosphor-file-jpg',
                    'png' => 'phosphor-file-png',
                    default => 'phosphor-file-text'
                }
                    : null)
                ->alignCenter()
                ->color('primary')
                ->size(IconColumnSize::ExtraLarge)
                ->extraAttributes(function ($record) {
                    if (filled($record->attachments)) {
                        $url = str_starts_with($record->attachments[0], 'http')
                            ? $record->attachments[0]
                            : config('filesystems.disks.s3.url') . $record->attachments[0];

                        return [
                            'x-on:click' => "SimpleLightBox.open(event, '{$url}')",
                            'src' => $url,
                        ];
                    }

                    return [];
                }, true);
        });

        Blueprint::macro('userstamps', function ($usingSoftDeletes = false) {
            /** @var Blueprint $this */
            $this->unsignedBigInteger('created_by_id')->nullable();
            $this->unsignedBigInteger('updated_by_id')->nullable();
            if ($usingSoftDeletes) {
                $this->unsignedBigInteger('deleted_by_id')->nullable();
            }
        });

        \Filament\Actions\CreateAction::configureUsing(function (\Filament\Actions\CreateAction $action) {
            $action
                ->icon('heroicon-o-plus');
        });

        \Filament\Actions\ImportAction::configureUsing(function ($action) {
            $action
                ->icon('heroicon-o-arrow-up-tray');
        });
    }
}
