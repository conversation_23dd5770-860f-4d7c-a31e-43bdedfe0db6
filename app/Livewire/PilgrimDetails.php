<?php

namespace App\Livewire;

use App\Models\Group;
use App\Models\Pilgrim;
use App\Models\Room;
use Filament\Infolists;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Pages\BasePage;
use Illuminate\Contracts\Support\Htmlable;

class PilgrimDetails extends BasePage
{
    protected static string $view = 'livewire.pilgrim-details';

    protected static string $layout = 'filament-panels::components.layout.simple';

    public Group $group;

    public Pilgrim $pilgrim;

    public ?Room $room;

    public function mount(string $code)
    {
        $ids = explode(':', base64_decode($code));
        $this->group = Group::where('token', $ids[0])->findOrFail($ids[1]);
        $this->pilgrim = $this->group->pilgrims()->findOrFail($ids[2]);
        $this->room = Room::find($this->pilgrim->pivot->room_id);
    }

    public function getHeading(): string | Htmlable
    {
        return '';
    }

    public function hasLogo(): bool
    {
        return false;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $handlers = collect($this->group->hotel_handlers)->keyBy('city');

        $hotels = $this->group->hotels;
        $hotels_makkah = $hotels->filter(fn ($hotel) => $hotel->city === 'Makkah');
        $hotels_madinah = $hotels->filter(fn ($hotel) => $hotel->city === 'Madinah');
        $room_numbers = collect($this->room->meta['room_numbers'] ?? []);
        $hotel_makkah_id = $room_numbers->whereIn('hotel_id', $hotels_makkah->pluck('id'))->where('room_number', '!=', '')->first()['hotel_id']
            ?? $hotels_makkah->first()?->id;
        $hotel_madinah_id = $room_numbers->whereIn('hotel_id', $hotels_madinah->pluck('id'))->where('room_number', '!=', '')->first()['hotel_id']
            ?? $hotels_madinah->first()?->id;

        return $infolist
            ->state([
                'group' => $this->group,
                'pilgrim' => $this->pilgrim,
                'room' => $this->room,
                'hotel_makkah' => $this->group->hotels()->where('hotels.id', $hotel_makkah_id)->pluck('name'),
                'hotel_madinah' => $this->group->hotels()->where('hotels.id', $hotel_madinah_id)->pluck('name'),
            ])
            ->inlineLabel()
            ->schema([
                Infolists\Components\ViewEntry::make('lugage_tag')
                    ->hiddenLabel()
                    ->view('components.pilgrim-luggage-tag', [
                        'pilgrim' => $this->pilgrim,
                        'group' => $this->group,
                    ]),
                Infolists\Components\ImageEntry::make('pilgrim.photo_url')
                    ->circular()
                    ->hiddenLabel()
                    ->label('Photo'),

                Infolists\Components\TextEntry::make('pilgrim.fullname')
                    ->label('Full Name'),

                Infolists\Components\TextEntry::make('pilgrim.passport_number')
                    ->label('Passport Number'),

                ...collect([
                    $this->group->tour_leader ? TextEntry::make('group.tour_leader.name')
                        ->label('Tour Leader')
                        ->helperText($this->group->tour_leader->phone)
                        ->suffixAction(
                            InfoLists\Components\Actions\Action::make('chat')
                                ->icon('tabler-brand-whatsapp')
                                ->color('success')
                                ->url(wa_chat_url($this->group->tour_leader->phone))
                                ->openUrlInNewTab()
                                ->visible((bool) $this->group->tour_leader->phone)
                        ) : null,
                ])->filter()->toArray(),

                ...collect([1, 2, 3])->map(function ($index) {
                    return Infolists\Components\TextEntry::make('group.mutawif' . ($index > 1 ? "_{$index}" : ''))
                        ->label('Mutawif' . ($index > 1 ? " {$index}" : ''))
                        ->formatStateUsing(fn ($state) => $state?->name)
                        ->helperText(fn ($state) => $state?->phone)
                        ->visible(fn ($state) => (bool) $state)
                        ->suffixAction(
                            fn ($state) => $state
                            ? InfoLists\Components\Actions\Action::make('chat')
                                ->icon('tabler-brand-whatsapp')
                                ->color('success')
                                ->url(wa_chat_url($state->phone))
                                ->openUrlInNewTab()
                                ->visible((bool) $state->phone)
                                : null
                        );
                })->toArray(),

                Infolists\Components\TextEntry::make('hotel_makkah')
                    ->label('Hotel Makkah')
                    ->listWithLineBreaks(),

                Infolists\Components\TextEntry::make('hotel_madinah')
                    ->label('Hotel Madinah')
                    ->listWithLineBreaks(),

                Infolists\Components\TextEntry::make('makkah_handlers')
                    ->label('Tim Makkah')
                    ->state($handlers->get('Makkah')['name'] ?? null)
                    ->helperText(fn ($state) => $handlers->get('Makkah')['phone'] ?? null)
                    ->suffixAction(
                        InfoLists\Components\Actions\Action::make('chat')
                            ->icon('tabler-brand-whatsapp')
                            ->color('success')
                            ->url(wa_chat_url($handlers->get('Makkah')['phone'] ?? null))
                            ->openUrlInNewTab()
                            ->visible((bool) ($handlers->get('Makkah')['phone'] ?? false))
                    ),

                Infolists\Components\TextEntry::make('madinah_handlers')
                    ->label('Tim Madinah')
                    ->state($handlers->get('Madinah')['name'] ?? null)
                    ->helperText(fn ($state) => $handlers->get('Madinah')['phone'] ?? null)
                    ->suffixAction(
                        InfoLists\Components\Actions\Action::make('chat')
                            ->icon('tabler-brand-whatsapp')
                            ->color('success')
                            ->url(wa_chat_url($handlers->get('Madinah')['phone'] ?? null))
                            ->openUrlInNewTab()
                            ->visible((bool) ($handlers->get('Madinah')['phone'] ?? false))
                    ),

                Infolists\Components\TextEntry::make('group.muassasah.company_name')
                    ->label('Muassasah'),
            ]);
    }
}
