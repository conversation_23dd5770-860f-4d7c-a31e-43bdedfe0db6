<?php

namespace App\Models;

use App\Enums\VendorType;
use App\Models\Finance\DebitNote;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vendor extends Model
{
    use HasFactory;

    protected $table = 'vendors';

    protected $fillable = [
        'original_id', // transitional

        'vendor_type',

        'company_name',

        'contact_name',
        'contact_email',
        'contact_phone',

        'meta',
    ];

    protected function casts()
    {
        return [
            'vendor_type' => VendorType::class,
            'meta' => 'array',
        ];
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_vendor', 'vendor_id', 'user_id');
    }

    public function bills(): HasMany
    {
        return $this->hasMany(Bill::class);
    }

    public function debitNotes(): Has<PERSON>any
    {
        return $this->hasMany(DebitNote::class);
    }
}
