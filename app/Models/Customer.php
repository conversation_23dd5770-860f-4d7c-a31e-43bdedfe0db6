<?php

namespace App\Models;

use App\Models\Finance\CreditNote;
use App\Models\Finance\CustomerCash;
use App\Models\Finance\Invoice;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON><PERSON>\PowerJoins\PowerJoins;
use Laravel\Sanctum\HasApiTokens;
use Ramsey\Uuid\Uuid;

class Customer extends Model implements AuthenticatableContract
{
    use Authenticatable;
    use HasApiTokens;
    use HasFactory;
    use PowerJoins;

    protected $fillable = [
        'uuid',
        'user_id',

        'name',
        'owner_name',
        'email',
        'phone',
        'logo',
        'logo_square',
        'password',
        'agent_id',

        'permit_no',
        'region', // domisili
        'address',
    ];

    protected $hidden = [
        'password',
    ];

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->uuid ??= Uuid::uuid4();
        });
    }

    public function logoUrl(): Attribute
    {
        return new Attribute(
            get: function () {
                if (blank($this->logo)) {
                    return null;
                }

                return str($this->logo)->startsWith('http') ? $this->logo : config('filesystems.disks.s3.url') . $this->logo;
            },
        );
    }

    public function logoSquareUrl(): Attribute
    {
        return new Attribute(
            get: function () {
                if (blank($this->logo_square)) {
                    return null;
                }

                return str($this->logo_square)->startsWith('http') ? $this->logo_square : config('filesystems.disks.s3.url') . $this->logo_square;
            },
        );
    }

    public function depositBalance(): Attribute
    {
        return Attribute::get(fn () => $this->deposits()->sum(DB::raw('CASE WHEN type = "d" THEN amount * exchange_rate ELSE amount * exchange_rate * -1 END')));
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    public function latest_group(): HasOne
    {
        return $this->hasOne(Group::class)->latest('arrival_date');
    }

    public function users(): HasMany
    {
        return $this->hasMany(CustomerUser::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function deposits(): HasMany
    {
        return $this->hasMany(CustomerCash::class);
    }

    public function creditNotes(): HasMany
    {
        return $this->hasMany(CreditNote::class);
    }

    public function delete()
    {
        if ($this->groups()->exists()) {
            return null;
        }

        return parent::delete();
    }

    public function group_cashes(): HasManyThrough
    {
        return $this->hasManyThrough(
            GroupCash::class,
            Group::class,
        );
    }
}
