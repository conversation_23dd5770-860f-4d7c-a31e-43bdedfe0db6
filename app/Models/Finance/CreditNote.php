<?php

namespace App\Models\Finance;

use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Customer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CreditNote extends Model implements JournalTransaction
{
    use HasJournalEntry;

    protected $fillable = [
        'customer_id',

        'invoice_date',
        'invoice_id',
        'used_for_invoice_id',

        'amount',
        'currency_code',
        'exchange_rate',
    ];

    protected function casts()
    {
        return [
            'invoice_date' => 'datetime:Y-m-d',
            'amount' => 'float',
            'exchange_rate' => 'float',
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->invoice_date ??= today();
        });
        static::saved(fn (self $model) => $model->syncJournalEntry());
    }

    public function getTransactionType(): string
    {
        return 'Credit Note';
    }

    public function getTransactionNumber(): string
    {
        $this->load('invoice');

        return $this->invoice->invoice_number;
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return null;
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function usedForInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'used_for_invoice_id');
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public function syncJournalEntry(): void
    {
        $entryDate = $this->invoice?->latestPayment?->paid_at ?? now();
        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $entryDate,
            'details' => "Credit Note for Invoice {$this->invoice?->invoice_number}",
        ]);

        $rate = ExchangeRate::getExchangeRate($entryDate, 'IDR', $this->currency_code);

        $entry->items()->delete();

        // Accounts Receivable
        $entry->items()->create([
            'type' => 'd',
            'account_id' => CashAccount::getIdForCode(config('finance.coa.receivable')),
            'currency_code' => $this->currency_code,
            'exchange_rate' => $rate,
            'amount' => $this->amount,
        ]);

        // Credit Note
        $entry->items()->create([
            'type' => 'c',
            'account_id' => CashAccount::getIdForCode(config('finance.coa.credit_note')),
            'currency_code' => $this->currency_code,
            'exchange_rate' => $rate,
            'amount' => $this->amount,
        ]);
    }
}
