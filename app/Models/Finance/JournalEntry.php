<?php

namespace App\Models\Finance;

use App\Models\Traits\WithUserstamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class JournalEntry extends Model
{
    use HasFactory;
    use WithUserstamps;

    protected $fillable = [
        'transaction_type',
        'transaction_id',

        'entry_date',
        'details',

        'created_by_id',
        'updated_by_id',

        'memo',
    ];

    protected $casts = [
        'entry_date' => 'datetime',
    ];

    public function transaction(): MorphTo
    {
        return $this->morphTo();
    }

    public function items(): HasMany
    {
        return $this->hasMany(JournalEntryItem::class, 'entry_id');
    }

    public function delete()
    {
        $this->items()->each(fn ($i) => $i->delete());

        return parent::delete();
    }
}
