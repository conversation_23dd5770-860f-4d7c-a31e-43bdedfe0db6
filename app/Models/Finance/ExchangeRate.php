<?php

namespace App\Models\Finance;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class ExchangeRate extends Model
{
    protected $fillable = [
        'rate_date',

        'base_currency',
        'currency',

        'exchange_rate',
    ];

    protected $casts = [
        'rate_date' => 'date',
        'exchange_rate' => 'float',
    ];

    public static function getExchangeRate(string | CarbonInterface $date, string $base, string $to)
    {
        if ($base === $to) {
            return 1;
        }

        if (! $date instanceof CarbonInterface) {
            $date = Carbon::parse($date);
        }

        return self::query()
            ->where('base_currency', $base)
            ->where('currency', $to)
            ->where('rate_date', '>=', $date->clone()->subDays(7)->format('Y-m-d'))
            ->where('rate_date', '<=', $date->format('Y-m-d'))
            ->latest('rate_date')
            ->value('exchange_rate');
    }

    public static function getLatestExchangeRate(string $base, string $to)
    {
        if ($base === $to) {
            return 1;
        }

        return self::query()
            ->where('base_currency', $base)
            ->where('currency', $to)
            ->latest('rate_date')
            ->value('exchange_rate');
    }
}
