<?php

namespace App\Models\Finance;

use App\Models\Bill;
use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DebitNote extends Model implements JournalTransaction
{
    use HasFactory;
    use HasJournalEntry;

    protected $fillable = [
        'vendor_id',

        'bill_date',
        'bill_id',
        'used_for_bill_id',

        'amount',
        'currency_code',
        'exchange_rate',
    ];

    protected function casts()
    {
        return [
            'bill_date' => 'datetime:Y-m-d',
            'amount' => 'float',
            'exchange_rate' => 'float',
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->bill_date ??= today();
        });
        static::saved(fn (self $model) => $model->syncJournalEntry());
    }

    public function getTransactionType(): string
    {
        return 'Debit Note';
    }

    public function getTransactionNumber(): string
    {
        $this->load('bill');

        return $this->bill->bill_number;
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return null;
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    public function usedForBill(): BelongsTo
    {
        return $this->belongsTo(Bill::class, 'used_for_bill_id');
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public function syncJournalEntry(): void
    {
        $entryDate = $this->bill?->latestPayment?->paid_at ?? now();

        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $entryDate,
            'details' => "Debit Note for Bill {$this->bill?->bill_number}",
        ]);

        $rate = ExchangeRate::getExchangeRate($entryDate, 'IDR', $this->currency_code);

        $entry->items()->delete();

        // Accounts Payable
        $entry->items()->create([
            'type' => 'c',
            'account_id' => CashAccount::getIdForCode(config('finance.coa.payable')),
            'currency_code' => $this->currency_code,
            'exchange_rate' => $rate,
            'amount' => $this->amount,
        ]);

        // Debit Note
        $entry->items()->create([
            'type' => 'd',
            'account_id' => CashAccount::getIdForCode(config('finance.coa.debit_note')),
            'currency_code' => $this->currency_code,
            'exchange_rate' => $rate,
            'amount' => $this->amount,
        ]);
    }
}
