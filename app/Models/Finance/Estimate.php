<?php

namespace App\Models\Finance;

use App\Enums\EstimateStatus;
use App\Models\Customer;
use App\Models\Group;
use App\Models\LA\Package;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class Estimate extends Model
{
    use HasFactory;

    const DEFAULT_TERMS = 'This estimate is valid for 30 days from the estimate date.';

    protected $fillable = [
        'estimate_date',
        'estimate_number',

        'customer_id',
        'group_id',
        'package_id',

        'valid_until',

        'currency_code',
        'exchange_rate',

        'subject',
        'notes',
        'terms',

        'total',
        'status',
        'invoice_id',

        'memo',
    ];

    protected $casts = [
        'estimate_date' => 'date',
        'valid_until' => 'date',
        'exchange_rate' => 'float',
        'total' => 'float',
        'status' => EstimateStatus::class,
    ];

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->estimate_number ??= static::getNextEstimateNumber();
        });

        static::saving(function (self $model) {
            $model->total = $model->getTotalEstimate();
        });
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(EstimateItem::class)->orderBy('order_column');
    }

    public function getTotalEstimate(): float
    {
        return floatval(DB::table('estimate_items')
            ->where('estimate_id', $this->id)
            ->selectRaw('SUM(quantity * unit_price) as subtotal')
            ->value('subtotal'));
    }

    public static function getNextEstimateNumber(): string
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "EST-{$periodYear}";

        $lastEstimate = static::query()
            ->where('estimate_number', 'like', $prefix . '%')
            ->orderBy('estimate_number', 'desc')
            ->first();

        $lastNumber = $lastEstimate ?
            (int) str_replace($prefix, '', $lastEstimate->estimate_number)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function convertToInvoice(): Invoice
    {
        if ($this->status === EstimateStatus::Invoiced) {
            return $this->invoice;
        }

        $invoice = Invoice::create([
            'invoice_date' => now(),
            'customer_id' => $this->customer_id,
            'group_id' => $this->group_id,
            'package_id' => $this->package_id,
            'due_date' => now()->addDays(30),
            'currency_code' => $this->currency_code,
            'exchange_rate' => $this->exchange_rate,
            'subject' => $this->subject,
            'notes' => $this->notes,
            'terms' => Invoice::getDefaultTerms(),
        ]);

        foreach ($this->items as $item) {
            $invoice->items()->create([
                'product_id' => $item->product_id,
                'name' => $item->name,
                'description' => $item->description,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
            ]);
        }

        $this->update([
            'status' => EstimateStatus::Invoiced,
            'invoice_id' => $invoice->id,
        ]);

        $invoice->save();

        return $invoice;
    }
}
