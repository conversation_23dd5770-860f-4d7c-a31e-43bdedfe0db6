<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class JournalEntryItem extends Model
{
    use HasFactory;

    const DEFAULT_RATES = [
        'SAR' => 4300,
        'USD' => 16000,
    ];

    protected $fillable = [
        'entry_id',

        'account_id',
        'type', // c / d

        'currency_code',
        'exchange_rate',
        'amount',

        'owner_type',
        'owner_id',
    ];

    protected function casts()
    {
        return [
            'exchange_rate' => 'float',
            'amount' => 'float',
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->exchange_rate ??= static::DEFAULT_RATES[$model->currency_code] ?? 1;
        });
    }

    public function entry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }

    public function owner(): MorphTo
    {
        return $this->morphTo();
    }
}
