<?php

namespace App\Models;

use App\Contracts\IsActivitySubject;
use App\Enums\ExpenseGroup;
use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Finance\CashAccount;
use App\Models\Finance\CashCategory;
use App\Models\Finance\ExchangeRate;
use App\Models\Traits\DefaultLogOptions;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;

class GroupCash extends Model implements IsActivitySubject, JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use HasJournalEntry;
    use LogsActivity;
    use SoftDeletes;

    const DIVISIONS = [
        // 'handling_arr' => 'Handling Arrival',
        // 'handling_dep' => 'Handling Departure',
        // 'mutawif' => 'Mutawif',
        // 'mutawif_2' => 'Mutawif 2',
        // 'mutawif_3' => 'Mutawif 3',
        'operator' => 'Operator',
        'check_in' => 'Check-In Team',
        // 'payment' => 'Payment',
    ];

    const CURRENCIES = [
        'SAR' => 'SAR',
        'IDR' => 'IDR',
    ];

    protected $fillable = [
        'user_id',
        'group_id',

        'division',

        'cashed_at',
        'category_id',
        'cash_in',
        'cash_out',

        'currency',
        'exchange_rate',

        'description',
        'is_excluded', // belum termasuk dalam harga paket
        'attachment',

        'hotel_broker_id',
        'institution_id',
    ];

    protected $casts = [
        'cashed_at' => 'datetime',
        'is_excluded' => 'boolean',
        'cash_in' => 'float',
        'cash_out' => 'float',
    ];

    public function getTransactionType(): string
    {
        return 'Operator';
    }

    public function getTransactionNumber(): string
    {
        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    protected static function boot()
    {
        static::creating(function ($model) {
            $model->exchange_rate ??= 1;
            if (! $model->user_id) {
                $model->user_id = auth()->user()->id;
            }
            if (! $model->is_excluded) {
                $model->is_excluded = false;
            }
        });
        static::saved(fn ($model) => $model->syncJournalEntry());

        parent::boot();
    }

    public function syncJournalEntry(): void
    {
        $this->load(['category']);

        if (
            $this->division === 'operator' &&
            $this->category?->group != ExpenseGroup::VendorPayment &&
            $this->category?->account_id
        ) {
            /** @var JournalEntry $entry */
            $entry = $this->journal_entry()->updateOrCreate([], [
                'entry_date' => $this->cashed_at,
                'details' => $this->category->name,
            ]);

            $rate = ExchangeRate::getExchangeRate($this->cashed_at, 'IDR', $this->currency);

            $cashAccountId = CashAccount::query()
                ->where('code', config('finance.coa.main_cash'))
                ->value('id');
            $debitAccountId = $this->cash_in
                ? $cashAccountId
                : $this->category->account_id;
            $creditAccountId = $this->cash_in
                ? CashAccount::query()
                    ->where('code', config('finance.coa.main_cash'))
                    ->value('id')
                : $cashAccountId;
            $amount = $this->cash_in ?? $this->cash_out;
            $entry->items()->updateOrCreate([
                'type' => 'd',
            ], [
                'account_id' => $debitAccountId,
                'currency_code' => $this->currency,
                'exchange_rate' => $rate,
                'amount' => $amount,
            ]);
            $entry->items()->updateOrCreate([
                'type' => 'c',
            ], [
                'account_id' => $creditAccountId,
                'currency_code' => $this->currency,
                'exchange_rate' => $rate,
                'amount' => $amount,
            ]);
        } else {
            $this->journal_entry?->delete();
        }
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->description;
    }

    public function getActivitySubjectDescription(Activity $activity): string
    {
        return $this->description;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(CashCategory::class, 'category_id');
    }

    public function hotel_broker(): BelongsTo
    {
        return $this->belongsTo(HotelBroker::class, 'hotel_broker_id');
    }

    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class);
    }

    public function scopeCurrentPeriod(Builder $query)
    {
        $period = current_period();

        $query->whereBetween('cashed_at', [
            $period->date_start->format('Y-m-d') . ' 00:00:00',
            $period->date_end->format('Y-m-d') . ' 23:59:59',
        ]);
    }

    public static function getAmountInputFormSchema()
    {
        return [
            Forms\Components\ToggleButtons::make('currency')
                ->options(static::CURRENCIES)
                ->default('SAR')
                ->inline()
                ->required()
                ->live()
                ->afterStateUpdated(function ($state, $set) {
                    $set('exchange_rate', Currency::getExchangeRate($state));
                }),
            Forms\Components\TextInput::make('amount')
                ->label('Nominal')
                ->numeric()
                ->prefix(fn ($get) => $get('currency'))
                ->required(),
            Forms\Components\TextInput::make('exchange_rate')
                ->numeric()
                ->prefix('SAR')
                ->default(1)
                ->required()
                ->live()
                ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency') . ' ' . round(1 / $state, 2) : null)
                ->visible(fn ($get) => $get('currency') != 'SAR'),
        ];
    }

    public static function getAmountTableColumns()
    {
        return [
            Tables\Columns\TextColumn::make('cash_in')
                ->label('Masuk')
                ->getStateUsing(fn ($record) => $record->cash_in
                ? $record->cash_in / $record->exchange_rate
                : null)
                ->description(function ($record) {
                    return $record->cash_in && $record->exchange_rate > 1
                        ? money($record->cash_in, $record->currency, true)
                        : null;
                })
                ->searchable()
                ->currencyRight(),
            Tables\Columns\TextColumn::make('cash_out')
                ->label('Keluar')
                ->getStateUsing(fn ($record) => $record->cash_out
                ? $record->cash_out / $record->exchange_rate
                : null)
                ->description(function ($record) {
                    return $record->cash_out && $record->exchange_rate > 1
                        ? money($record->cash_out, $record->currency, true)
                        : null;
                })
                ->searchable()
                ->currencyRight(),
        ];
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public function attachmentUrl(): Attribute
    {
        return new Attribute(
            get: function () {
                if (blank($this->attachment)) {
                    return null;
                }

                return str($this->attachment)->startsWith('http') ? $this->attachment : config('filesystems.disks.s3.url') . $this->attachment;
            },
        );
    }
}
