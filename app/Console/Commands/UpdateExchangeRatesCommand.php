<?php

namespace App\Console\Commands;

use App\Models\Finance\ExchangeRate;
use App\Services\BankIndonesiaExchangeRate;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateExchangeRatesCommand extends Command
{
    protected $signature = 'exchange:update {currency=SAR} {--days=30}';

    protected $description = 'Update exchange rates from Bank Indonesia and save mid-rates to database';

    public function handle(BankIndonesiaExchangeRate $biService)
    {
        $currency = strtoupper($this->argument('currency'));
        $days = $this->option('days');

        $endDate = now()->format('Y-m-d');
        $startDate = now()->subDays($days)->format('Y-m-d');

        $this->info("Fetching {$currency} exchange rates from {$startDate} to {$endDate}");

        try {
            $data = $biService->getExchangeRates($currency, $startDate, $endDate);

            if (empty($data['data'])) {
                $this->warn("No exchange rate data found for {$currency}");

                return;
            }

            $this->info("Retrieved {$data['summary']['total_records']} records for {$currency}");

            $savedCount = 0;
            $updatedCount = 0;
            $skippedCount = 0;

            // Create progress bar
            $progressBar = $this->output->createProgressBar(count($data['data']));
            $progressBar->start();

            foreach ($data['data'] as $rateData) {
                $result = $this->saveExchangeRate($rateData);

                switch ($result) {
                    case 'created':
                        $savedCount++;
                        break;
                    case 'updated':
                        $updatedCount++;
                        break;
                    case 'skipped':
                        $skippedCount++;
                        break;
                }

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            // Summary
            $this->info('Exchange rate update completed:');
            $this->table(['Action', 'Count'], [
                ['Created', $savedCount],
                ['Updated', $updatedCount],
                ['Skipped', $skippedCount],
                ['Total Processed', $savedCount + $updatedCount + $skippedCount],
            ]);

            // Show latest rate info
            if ($latestRate = $data['summary']['latest_rate']) {
                $this->info("Latest {$currency} mid-rate ({$latestRate['date']}): " . number_format($latestRate['mid_rate'], 4) . ' IDR');
            }

        } catch (Exception $e) {
            $this->error('Failed to update exchange rates: ' . $e->getMessage());
            Log::error('UpdateExchangeRatesCommand failed', [
                'currency' => $currency,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Save exchange rate data to database (always mid-rate)
     */
    private function saveExchangeRate(array $rateData)
    {
        $exchangeRate = ExchangeRate::updateOrCreate(
            [
                'rate_date' => $rateData['date'],
                'base_currency' => 'IDR',
                'currency' => $rateData['currency'],
            ],
            [
                'exchange_rate' => $rateData['mid_rate'],
            ]
        );

        if ($exchangeRate->wasRecentlyCreated) {
            return 'created';
        } elseif ($exchangeRate->wasChanged()) {
            return 'updated';
        } else {
            return 'skipped';
        }
    }
}
