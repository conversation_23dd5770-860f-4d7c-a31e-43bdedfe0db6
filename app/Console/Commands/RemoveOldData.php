<?php

namespace App\Console\Commands;

use App\Models\BillPayment;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use App\Models\Finance\JournalEntry;
use App\Models\Finance\UserCash;
use App\Models\GroupCash;
use Illuminate\Console\Command;

class RemoveOldData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-old-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cutoffDate = '2024-07-07';

        $invoices = Invoice::query()
            ->with(['payments', 'refunds', 'journal_entry', 'creditNote'])
            ->where('invoice_number', 'like', 'INV-1443%')
            ->orWhere('invoice_number', 'like', ' INV-1443%')
            ->orWhere('invoice_number', 'like', '1443%')
            ->orWhere('invoice_number', '0')
            ->get();

        $bar = $this->output->createProgressBar($invoices->count());
        $bar->start();

        $invoices->each(function ($inv) use ($bar) {
            $inv->payments->each->delete();
            $inv->refunds->each->delete();
            $inv->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$invoices->count()} invoice deleted.");

        $invoicePayments = InvoicePayment::query()
            ->with(['journal_entry', 'customer_cash'])
            ->leftJoin('invoices', 'invoices.id', '=', 'invoice_payments.invoice_id')
            ->select([
                'invoice_payments.*',
                'invoices.invoice_number',
            ])
            ->whereNull('invoice_number')
            ->get();

        $bar = $this->output->createProgressBar($invoicePayments->count());
        $bar->start();

        $invoicePayments->each(function ($payment) use ($bar) {
            $payment->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$invoicePayments->count()} invoice payments deleted.");

        $groupCashes = GroupCash::query()
            ->with(['journal_entry'])
            ->whereDate('cashed_at', '<', $cutoffDate)
            ->get();

        $bar = $this->output->createProgressBar($groupCashes->count());
        $bar->start();

        $groupCashes->each(function ($item) use ($bar) {
            $item->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$groupCashes->count()} group cash transactions deleted.");

        $cashAdvances = UserCash::query()
            ->with(['journal_entry'])
            ->whereDate('cashed_at', '<', $cutoffDate)
            ->get();

        $bar = $this->output->createProgressBar($cashAdvances->count());
        $bar->start();

        $cashAdvances->each(function ($item) use ($bar) {
            if ($item->related_type === 'bill') {
                BillPayment::query()
                    ->where('user_cash_id', $item->id)
                    ->get()->each?->delete();
            }
            $item->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$cashAdvances->count()} cash advance transactions deleted.");

        // Bill journal entries
        $journalEntries = JournalEntry::query()
            ->leftJoin('bills', 'bills.id', '=', 'journal_entries.transaction_id')
            ->where('transaction_type', 'bill')
            ->select([
                'journal_entries.*',
                'bills.bill_number',
            ])
            ->whereNull('bill_number')
            ->get();

        $bar = $this->output->createProgressBar($journalEntries->count());
        $bar->start();

        $journalEntries->each(function ($item) use ($bar) {
            $item->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$journalEntries->count()} orphaned bill journal entries deleted.");

        // Bill payment journal entries
        $journalEntries = JournalEntry::query()
            ->leftJoin('bill_payments', 'bill_payments.id', '=', 'journal_entries.transaction_id')
            ->where('transaction_type', 'bill_payment')
            ->select([
                'journal_entries.*',
                'bill_payments.id AS payment_id',
            ])
            ->havingNull('payment_id')
            ->get();

        $bar = $this->output->createProgressBar($journalEntries->count());
        $bar->start();

        $journalEntries->each(function ($item) use ($bar) {
            $item->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$journalEntries->count()} orphaned bill payment journal entries deleted.");

        // Invoice journal entries
        $journalEntries = JournalEntry::query()
            ->leftJoin('invoices', 'invoices.id', '=', 'journal_entries.transaction_id')
            ->where('transaction_type', 'invoice')
            ->select([
                'journal_entries.*',
                'invoices.invoice_number',
            ])
            ->whereNull('invoice_number')
            ->get();

        $bar = $this->output->createProgressBar($journalEntries->count());
        $bar->start();

        $journalEntries->each(function ($item) use ($bar) {
            $item->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$journalEntries->count()} orphaned invoice journal entries deleted.");

        // Invoice payment journal entries
        $journalEntries = JournalEntry::query()
            ->leftJoin('invoice_payments', 'invoice_payments.id', '=', 'journal_entries.transaction_id')
            ->where('transaction_type', 'invoice_payment')
            ->select([
                'journal_entries.*',
                'invoice_payments.id AS payment_id',
            ])
            ->havingNull('payment_id')
            ->get();

        $bar = $this->output->createProgressBar($journalEntries->count());
        $bar->start();

        $journalEntries->each(function ($item) use ($bar) {
            $item->delete();

            $bar->advance();
        });

        $bar->finish();
        $this->info("\n{$journalEntries->count()} orphaned invoice payment journal entries deleted.");
    }
}
