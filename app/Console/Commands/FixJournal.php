<?php

namespace App\Console\Commands;

use App\Models\Bill;
use App\Models\BillPayment;
use App\Models\Finance\CashTransaction;
use App\Models\Finance\CreditNote;
use App\Models\Finance\CustomerCash;
// use App\Models\Finance\DebitNote;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use App\Models\Finance\InvoiceRefund;
use App\Models\Finance\UserCash;
use App\Models\GroupCash;
use Illuminate\Console\Command;

class FixJournal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-journal {--days=30}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');

        $endDate = now()->format('Y-m-d');
        $startDate = now()->subDays($days)->format('Y-m-d');

        $models = [
            [Bill::class, 'bill_date'],
            [BillPayment::class, 'paid_at'],
            [GroupCash::class, 'cashed_at'],
            [CashTransaction::class, 'transaction_date'],
            [CreditNote::class, 'invoice_date'],
            [CustomerCash::class, 'cashed_at'],
            // [DebitNote::class, 'bill_date'],
            [Invoice::class, 'invoice_date'],
            [InvoicePayment::class, 'paid_at'],
            [InvoiceRefund::class, 'refunded_at'],
            [UserCash::class, 'cashed_at'],
        ];

        $total = 0;
        $items = [];
        foreach ($models as [$model, $dateField]) {
            $query = $model::query()
                ->whereDate($dateField, '>=', $startDate)
                ->whereDate($dateField, '<=', $endDate);
            $results = $query->get();
            $total += $results->count();
            $items[] = [$results, $model];
        }

        $bar = $this->output->createProgressBar($total);
        $bar->start();

        foreach ($items as [$results, $model]) {
            foreach ($results as $item) {
                $item->syncJournalEntry();
                $bar->advance();
            }
        }
        $bar->finish();
        $this->info("\nJournal entries synced for last {$days} days.");
    }
}
