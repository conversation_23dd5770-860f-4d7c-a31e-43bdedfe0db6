<?php

namespace App\Services;

use DateTime;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class BankIndonesiaExchangeRate
{
    private $baseUrl = 'https://www.bi.go.id/biwebservice/wskursbi.asmx/getSubKursLokal3';

    private $timeout = 30;

    /**
     * Get exchange rate data from Bank Indonesia API
     *
     * @param  string  $currency  Currency code (e.g., 'SAR', 'USD', 'EUR')
     * @param  string  $startDate  Start date in YYYY-MM-DD format
     * @param  string  $endDate  End date in YYYY-MM-DD format
     * @return array Parsed exchange rate data
     *
     * @throws Exception If API call fails or data parsing fails
     */
    public function getExchangeRates($currency, $startDate, $endDate)
    {
        Log::info('BankIndonesiaExchangeRate: Starting API call', [
            'currency' => $currency,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        try {
            // Validate input parameters
            $this->validateInputs($currency, $startDate, $endDate);

            // Make API call
            $xmlData = $this->makeApiCall($currency, $startDate, $endDate);

            // Parse XML response
            $parsedData = $this->parseXmlResponse($xmlData);

            Log::info('BankIndonesiaExchangeRate: API call successful', [
                'currency' => $currency,
                'total_records' => $parsedData['summary']['total_records'],
            ]);

            return $parsedData;

        } catch (Exception $e) {
            Log::error('BankIndonesiaExchangeRate: API call failed', [
                'currency' => $currency,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Validate input parameters
     */
    private function validateInputs($currency, $startDate, $endDate)
    {
        if (empty($currency)) {
            throw new InvalidArgumentException('Currency code is required');
        }

        if (! $this->isValidDate($startDate)) {
            throw new InvalidArgumentException('Invalid start date format. Use YYYY-MM-DD');
        }

        if (! $this->isValidDate($endDate)) {
            throw new InvalidArgumentException('Invalid end date format. Use YYYY-MM-DD');
        }

        if (strtotime($startDate) > strtotime($endDate)) {
            throw new InvalidArgumentException('Start date cannot be after end date');
        }
    }

    /**
     * Check if date is in valid format
     */
    private function isValidDate($date)
    {
        $d = DateTime::createFromFormat('Y-m-d', $date);

        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Make API call using Laravel Http facade
     */
    private function makeApiCall($currency, $startDate, $endDate)
    {
        Log::debug('BankIndonesiaExchangeRate: Making HTTP request', [
            'url' => $this->baseUrl,
            'params' => [
                'mts' => strtoupper($currency),
                'startdate' => $startDate,
                'enddate' => $endDate,
            ],
        ]);

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Accept' => 'application/xml, text/xml',
                'Cache-Control' => 'no-cache',
                'User-Agent' => 'Laravel Exchange Rate Client/1.0',
            ])
            ->get($this->baseUrl, [
                'mts' => strtoupper($currency),
                'startdate' => $startDate,
                'enddate' => $endDate,
            ]);

        if ($response->failed()) {
            $errorMessage = 'HTTP request failed with status: ' . $response->status();

            if ($response->serverError()) {
                $errorMessage .= ' (Server Error)';
            } elseif ($response->clientError()) {
                $errorMessage .= ' (Client Error)';
            }

            Log::error('BankIndonesiaExchangeRate: HTTP request failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'headers' => $response->headers(),
            ]);

            throw new Exception($errorMessage);
        }

        $responseBody = $response->body();

        if (empty($responseBody)) {
            throw new Exception('Empty response received from API');
        }

        Log::debug('BankIndonesiaExchangeRate: HTTP request successful', [
            'status' => $response->status(),
            'content_length' => strlen($responseBody),
        ]);

        return $responseBody;
    }

    /**
     * Parse XML response into structured array
     */
    private function parseXmlResponse($xmlData)
    {
        Log::debug('BankIndonesiaExchangeRate: Starting XML parsing');

        // Suppress XML parsing errors
        libxml_use_internal_errors(true);

        $xml = simplexml_load_string($xmlData);

        if ($xml === false) {
            $errors = libxml_get_errors();
            $errorMessage = 'XML Parsing Error: ';
            foreach ($errors as $error) {
                $errorMessage .= $error->message . ' ';
            }

            Log::error('BankIndonesiaExchangeRate: XML parsing failed', [
                'errors' => $errors,
                'xml_sample' => substr($xmlData, 0, 500),
            ]);

            throw new Exception(trim($errorMessage));
        }

        // Register namespaces
        $xml->registerXPathNamespace('diffgr', 'urn:schemas-microsoft-com:xml-diffgram-v1');

        $result = [
            'currency' => null,
            'data' => [],
            'summary' => [
                'total_records' => 0,
                'date_range' => [
                    'start' => null,
                    'end' => null,
                ],
                'latest_rate' => null,
            ],
        ];

        // Find all Table elements in the dataset
        $tables = $xml->xpath('//Table');

        if (empty($tables)) {
            Log::warning('BankIndonesiaExchangeRate: No data found in XML response');

            return $result;
        }

        Log::debug('BankIndonesiaExchangeRate: Found tables in XML', [
            'table_count' => count($tables),
        ]);

        $exchangeRates = [];
        $dates = [];

        foreach ($tables as $table) {
            $rate = [
                'id' => (int) $table->id_subkurslokal,
                'link_id' => (int) $table->lnk_subkurslokal,
                'value' => (float) $table->nil_subkurslokal,
                'buy_rate' => (float) $table->beli_subkurslokal,
                'sell_rate' => (float) $table->jual_subkurslokal,
                'date' => $this->parseDate((string) $table->tgl_subkurslokal),
                'currency' => trim((string) $table->mts_subkurslokal),
                'spread' => (float) $table->jual_subkurslokal - (float) $table->beli_subkurslokal,
                'mid_rate' => ((float) $table->beli_subkurslokal + (float) $table->jual_subkurslokal) / 2,
            ];

            $exchangeRates[] = $rate;
            $dates[] = $rate['date'];
        }

        // Sort by date (newest first)
        usort($exchangeRates, function ($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        // Set result data
        $result['currency'] = ! empty($exchangeRates) ? $exchangeRates[0]['currency'] : null;
        $result['data'] = $exchangeRates;
        $result['summary']['total_records'] = count($exchangeRates);

        if (! empty($dates)) {
            sort($dates);
            $result['summary']['date_range']['start'] = $dates[0];
            $result['summary']['date_range']['end'] = end($dates);
            $result['summary']['latest_rate'] = $exchangeRates[0];
        }

        Log::debug('BankIndonesiaExchangeRate: XML parsing completed', [
            'total_records' => $result['summary']['total_records'],
            'currency' => $result['currency'],
            'date_range' => $result['summary']['date_range'],
        ]);

        return $result;
    }

    /**
     * Parse date string to standard format
     */
    private function parseDate($dateString)
    {
        $date = new DateTime($dateString);

        return $date->format('Y-m-d');
    }

    /**
     * Get available currency codes (common ones)
     */
    public function getAvailableCurrencies()
    {
        return [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'JPY' => 'Japanese Yen',
            'GBP' => 'British Pound',
            'AUD' => 'Australian Dollar',
            'CAD' => 'Canadian Dollar',
            'CHF' => 'Swiss Franc',
            'CNY' => 'Chinese Yuan',
            'SGD' => 'Singapore Dollar',
            'MYR' => 'Malaysian Ringgit',
            'THB' => 'Thai Baht',
            'KRW' => 'South Korean Won',
            'SAR' => 'Saudi Arabian Riyal',
            'AED' => 'UAE Dirham',
            'HKD' => 'Hong Kong Dollar',
        ];
    }

    /**
     * Set request timeout in seconds
     */
    public function setTimeout($seconds)
    {
        $this->timeout = (int) $seconds;

        return $this;
    }

    /**
     * Get exchange rates with caching support
     */
    public function getExchangeRatesWithCache($currency, $startDate, $endDate, $cacheMinutes = 60)
    {
        $cacheKey = "bi_exchange_rates_{$currency}_{$startDate}_{$endDate}";

        return cache()->remember($cacheKey, now()->addMinutes($cacheMinutes), function () use ($currency, $startDate, $endDate) {
            return $this->getExchangeRates($currency, $startDate, $endDate);
        });
    }

    /**
     * Get latest exchange rate for a currency
     */
    public function getLatestRate($currency)
    {
        $endDate = now()->format('Y-m-d');
        $startDate = now()->subDays(7)->format('Y-m-d'); // Get last 7 days to ensure we have data

        $data = $this->getExchangeRates($currency, $startDate, $endDate);

        return $data['summary']['latest_rate'] ?? null;
    }
}

// Usage examples in Laravel:

/*
// In a Controller:
class ExchangeRateController extends Controller
{
    private $biService;

    public function __construct(BankIndonesiaExchangeRate $biService)
    {
        $this->biService = $biService;
    }

    public function getSarRates(Request $request)
    {
        try {
            $startDate = $request->get('start_date', '2025-07-01');
            $endDate = $request->get('end_date', '2025-07-30');

            $data = $this->biService->getExchangeRatesWithCache('SAR', $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getLatestUsdRate()
    {
        try {
            $latestRate = $this->biService->getLatestRate('USD');

            return response()->json([
                'success' => true,
                'data' => $latestRate
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}

// In a Service Provider (to register as singleton):
class AppServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(BankIndonesiaExchangeRate::class, function ($app) {
            return new BankIndonesiaExchangeRate();
        });
    }
}

// In a Command or Job:
class UpdateExchangeRatesCommand extends Command
{
    protected $signature = 'exchange:update {currency=USD}';
    protected $description = 'Update exchange rates from Bank Indonesia';

    public function handle(BankIndonesiaExchangeRate $biService)
    {
        $currency = $this->argument('currency');
        $endDate = now()->format('Y-m-d');
        $startDate = now()->subDays(30)->format('Y-m-d');

        try {
            $data = $biService->getExchangeRates($currency, $startDate, $endDate);

            $this->info("Successfully retrieved {$data['summary']['total_records']} records for {$currency}");

            if ($latestRate = $data['summary']['latest_rate']) {
                $this->info("Latest {$currency} rate: Buy {$latestRate['buy_rate']}, Sell {$latestRate['sell_rate']}");
            }

        } catch (Exception $e) {
            $this->error("Failed to update exchange rates: " . $e->getMessage());
        }
    }
}
*/
