<?php

use App\Contracts\PdfService;
use App\Contracts\WhatsAppService;
use App\Models\Period;
use App\Models\User;
use App\Settings\GeneralSettings;

function auth_user(): ?User
{
    return auth('web')->user();
}

function current_period(): Period
{
    $period_id = config('period_id');
    if (! $period_id) {
        $period_id = app(GeneralSettings::class)->period_id;
    }
    if (! $period_id) {
        $period_id = 1;
    }

    return Period::query()->find($period_id) ?? Period::query()->first();
}

function is_serialized($data, $strict = true)
{
    // If it isn't a string, it isn't serialized.
    if (! is_string($data)) {
        return false;
    }
    $data = trim($data);
    if ($data === 'N;') {
        return true;
    }
    if (strlen($data) < 4) {
        return false;
    }
    if ($data[1] !== ':') {
        return false;
    }
    if ($strict) {
        $lastc = substr($data, -1);
        if ($lastc !== ';' && $lastc !== '}') {
            return false;
        }
    } else {
        $semicolon = strpos($data, ';');
        $brace = strpos($data, '}');
        // Either ; or } must exist.
        if ($semicolon === false && $brace === false) {
            return false;
        }
        // But neither must be in the first X characters.
        if ($semicolon !== false && $semicolon < 3) {
            return false;
        }
        if ($brace !== false && $brace < 4) {
            return false;
        }
    }
    $token = $data[0];
    switch ($token) {
        case 's':
            if ($strict) {
                if (substr($data, -2, 1) !== '"') {
                    return false;
                }
            } elseif (strpos($data, '"') === false) {
                return false;
            }
            // Or else fall through.
        case 'a':
        case 'O':
        case 'E':
            return (bool) preg_match("/^{$token}:[0-9]+:/s", $data);
        case 'b':
        case 'i':
        case 'd':
            $end = $strict ? '$' : '';

            return (bool) preg_match("/^{$token}:[0-9.E+-]+;{$end}/", $data);
    }

    return false;
}

function maybe_serialize($data)
{
    if (is_array($data) || is_object($data)) {
        return serialize($data);
    }

    return $data;
}

function maybe_unserialize($data)
{
    if (is_serialized($data)) { // Don't attempt to unserialize data that wasn't serialized going in.
        return @unserialize(trim($data));
    }

    return $data;
}

function whatsapp_link($phone)
{
    if (! $phone || $phone === '-') {
        return '';
    }

    // FIXME: remove this when db data fixed
    $phone = format_phone_for_db($phone);

    return '<a class="text-success-700" target="_blank" href="' . wa_chat_url($phone) . '">' . format_phone($phone) . '</a>';
}

function sanitize_filename($filename): string
{
    // Remove any directory traversal attempts
    $filename = basename($filename);

    // Convert to UTF-8 if needed
    if (! mb_check_encoding($filename, 'UTF-8')) {
        $filename = mb_convert_encoding($filename, 'UTF-8');
    }

    // Replace any character that isn't alphanumeric, space, dot, hyphen, parentheses or hash
    $filename = preg_replace('/[^[:alnum:][:space:]\.\-\(\)\#]/u', '', $filename);

    // Trim spaces from beginning and end
    $filename = trim($filename);

    // Ensure the filename isn't empty after sanitization
    return empty($filename) ? 'unnamed_file' : $filename;
}

/**
 * Create a short, fairly unique, urlsafe hash for the input string.
 */
function generate_id($input, $length = 8)
{
    // Create a raw binary sha256 hash and base64 encode it.
    $hash_base64 = base64_encode(hash('sha256', $input, true));
    // Replace non-urlsafe chars to make the string urlsafe.
    $hash_urlsafe = strtr($hash_base64, '+/', '-_');
    // Trim base64 padding characters from the end.
    $hash_urlsafe = rtrim($hash_urlsafe, '=');

    // Shorten the string before returning.
    return substr($hash_urlsafe, 0, $length);
}

function format_phone_for_db($phone)
{
    $phone = preg_replace('/\D/', '', $phone);

    if (substr($phone, 0, 2) === '05') {
        return '+966' . substr($phone, 1);
    }

    if (substr($phone, 0, 1) === '0') {
        return '+62' . substr($phone, 1);
    }

    return blank($phone) ? null : '+' . $phone;
}

function format_phone($phone)
{
    return phone($phone)->formatInternational();
}

function nbsp()
{
    return ' ';
}

function wa_format_phone($phone): string
{
    $phone = preg_replace('/[^0-9]/', '', $phone);
    $phone = substr($phone, 0, 1) == '0' ? '62' . substr($phone, 1) : $phone;

    return $phone;
}

function wa_chat_url(?string $phone): ?string
{
    return $phone
        ? 'https://wa.me/' . wa_format_phone($phone)
        : null;
}

function rrmdir($dir)
{
    if (is_dir($dir)) {
        $objects = scandir($dir);

        foreach ($objects as $object) {
            if ($object != '.' && $object != '..') {
                if (filetype($dir . '/' . $object) == 'dir') {
                    rrmdir($dir . '/' . $object);
                } else {
                    unlink($dir . '/' . $object);
                }
            }
        }

        reset($objects);
        rmdir($dir);
    }
}

function whatsapp(): WhatsAppService
{
    return app(WhatsAppService::class);
}

function pdf(): PdfService
{
    return app(PdfService::class);
}

function evaluate_math_string($str)
{
    // Input validation and sanitization
    if (empty($str) || ! is_string($str)) {
        return 0;
    }

    // Remove all non-math characters and normalize
    $str = preg_replace('/[^\d.+\-*\/()]/i', '', $str);
    $str = trim($str, '/*+');
    $str = rtrim($str, '-');

    if (empty($str)) {
        return 0;
    }

    return evaluateExpression($str);
}

function evaluateExpression($str)
{
    // Handle parentheses first (recursive descent)
    while (preg_match('/\(([^()]+)\)/', $str, $matches, PREG_OFFSET_CAPTURE)) {
        $innerExpression = $matches[1][0];
        $result = evaluateExpression($innerExpression);

        // Replace the parentheses group with the result
        $str = substr_replace($str, $result, $matches[0][1], strlen($matches[0][0]));
    }

    // Remove any remaining parentheses (shouldn't happen with valid input)
    $str = str_replace(['(', ')'], '', $str);

    // Normalize double operators
    $str = normalizeOperators($str);

    // Process multiplication and division (left to right)
    $str = processOperators($str, ['*', '/']);

    // Process addition and subtraction (left to right)
    $str = processOperators($str, ['+', '-']);

    return (float) $str;
}

function normalizeOperators($str)
{
    // Handle multiple consecutive operators
    $replacements = [
        '++' => '+',
        '--' => '+',
        '+-' => '-',
        '-+' => '-',
    ];

    do {
        $oldStr = $str;
        $str = str_replace(array_keys($replacements), array_values($replacements), $str);
    } while ($str !== $oldStr);

    return $str;
}

function processOperators($str, $operators)
{
    foreach ($operators as $operator) {
        // Use a more precise regex that handles negative numbers
        $pattern = $operator === '-'
            ? '/(^|[+\-*\/])([\d.]+)\-(\d+\.?\d*)/'
            : '/([\d.]+)\\' . $operator . '(\-?[\d.]+)/';

        while (preg_match($pattern, $str, $matches)) {
            $left = (float) ($operator === '-' && $matches[1] !== '' ?
                ($matches[1] === '-' ? -$matches[2] : $matches[2]) : $matches[1]);
            $right = (float) $matches[$operator === '-' ? 3 : 2];

            $result = calculateOperation($left, $right, $operator);

            if ($result === false) {
                return '0'; // Division by zero
            }

            // Replace the matched operation with the result
            if ($operator === '-' && $matches[1] !== '') {
                $str = str_replace($matches[1] . $matches[2] . '-' . $matches[3],
                    $matches[1] . $result, $str);
            } else {
                $str = str_replace($matches[0], $result, $str);
            }

            $str = normalizeOperators($str);
        }
    }

    // Handle simple addition/subtraction chain
    if (in_array('+', $operators) || in_array('-', $operators)) {
        $str = evaluateAdditionSubtraction($str);
    }

    return $str;
}

function calculateOperation($left, $right, $operator)
{
    switch ($operator) {
        case '+': return $left + $right;
        case '-': return $left - $right;
        case '*': return $left * $right;
        case '/':
            if ($right == 0) {
                return false; // Division by zero
            }

            return $left / $right;
        default: return 0;
    }
}

function evaluateAdditionSubtraction($str)
{
    // Handle chains of addition and subtraction
    if (! preg_match('/[+\-]/', $str)) {
        return $str;
    }

    // Split into tokens while preserving operators
    preg_match_all('/([\d.]+|[+\-])/', $str, $matches);

    if (! isset($matches[0]) || empty($matches[0])) {
        return '0';
    }

    $tokens = $matches[0];
    $result = 0;
    $operator = '+';

    // Handle leading negative sign
    $startIndex = 0;
    if ($tokens[0] === '-') {
        $operator = '-';
        $startIndex = 1;
    } elseif ($tokens[0] === '+') {
        $startIndex = 1;
    }

    for ($i = $startIndex; $i < count($tokens); $i++) {
        $token = $tokens[$i];

        if ($token === '+' || $token === '-') {
            $operator = $token;
        } else {
            $value = (float) $token;
            $result = ($operator === '+') ? ($result + $value) : ($result - $value);
        }
    }

    return (string) $result;
}
