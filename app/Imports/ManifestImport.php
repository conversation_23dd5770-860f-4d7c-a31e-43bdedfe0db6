<?php

namespace App\Imports;

use App\Models\GroupPilgrim;
use App\Models\Pilgrim;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Row;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class ManifestImport implements OnEachRow, SkipsEmptyRows, SkipsOnFailure, WithHeadingRow, WithValidation
{
    use SkipsFailures;

    protected $group_id = null;

    public function __construct(
        public string $model,
        public array $attributes = []
    ) {
        $this->group_id = $attributes['group_id'] ?? null;
    }

    public function rules(): array
    {
        return [
            'passport_number' => ['required'],
            'national_id' => ['nullable'],
            'full_name' => ['required'],
            'gender' => ['required', 'in:m,f'],
            'title' => ['required', 'in:mr,mrs,ms'],
            'place_of_birth' => ['nullable'],
            'date_of_birth' => ['required', 'date_format:Y-m-d'],

            'first' => ['nullable'],
            'last' => ['nullable'],
            'father' => ['nullable'],
            'grand' => ['nullable'],
        ];
    }

    public function prepareForValidation($data, $index)
    {
        $data['passport_number'] = $data['passport_number'] ?? $data['ppno'] ?? null;

        $data['full_name'] = $data['full_name'] ?? $data['fullname'] ?? null;

        $data['gender'] = match ($data['gender'] ?? null) {
            'Male', 'male', 'M', 'm' => 'm',
            'Female', 'female', 'F', 'f' => 'f',
            default => null
        };

        $data['title'] = match ($data['title'] ?? null) {
            'Mr', 'mr' => 'mr',
            'Mrs', 'mrs' => 'mrs',
            'Miss', 'miss', 'ms' => 'ms',
            default => null
        };

        $data['place_of_birth'] = $data['place_of_birth'] ?? $data['birthcity'] ?? null;

        if (isset($data['birthdate'])) {
            try {
                $data['date_of_birth'] = Date::excelToDateTimeObject($data['birthdate'])->format('Y-m-d');
            } catch (\Throwable $th) {
                $data['date_of_birth'] = $data['birthdate'];
            }
        } elseif (isset($data['date_of_birth'])) {
            try {
                $data['date_of_birth'] = Date::excelToDateTimeObject($data['date_of_birth'])->format('Y-m-d');
            } catch (\Throwable $th) {
                //
            }
        }

        if (isset($data['phone'])) {
            $data['phone'] = format_phone_for_db($data['phone']);
        }

        return $data;
    }

    public function onRow(Row $row)
    {
        $data = $row->toArray();

        if (blank($data['national_id'] ?? null) && blank($data['passport_number'] ?? null)) {
            return;
        }

        if (filled($data['national_id'] ?? null)) {
            $pilgrim = Pilgrim::where('national_id', $data['national_id'])
                ->first();
        } else {
            $pilgrim = Pilgrim::where('passport_number', $data['passport_number'])
                ->first();
        }
        if (preg_match('/\p{Arabic}/u', $data['full_name'] ?? '')) {
            $parts = [];
            if (! empty($data['first'])) {
                $parts[] = $data['first'];
            }
            if (! empty($data['father'])) {
                $parts[] = $data['father'];
            }
            if (! empty($data['grand'])) {
                $parts[] = $data['grand'];
            }
            if (! empty($data['last'])) {
                $parts[] = $data['last'];
            }
            if ($parts) {
                $data['full_name'] = implode(' ', $parts);
            }
        }

        $modelData = [
            'passport_number' => $data['passport_number'] ?? null,
            'national_id' => $data['national_id'] ?? null,

            'fullname' => $data['full_name'],
            'gender' => $data['gender'],
            'title' => $data['title'],

            'birthplace' => $data['place_of_birth'] ?? null,
            'birthdate' => $data['date_of_birth'] ?? null,

            'phone' => $data['phone'] ?? null,
        ];
        if ($pilgrim) {
            $pilgrim->update($modelData);
        } else {
            $pilgrim = Pilgrim::create($modelData);
        }

        if ($this->group_id) {
            // $pilgrim->groups()->attach($this->group_id);
            GroupPilgrim::query()->firstOrCreate([
                'group_id' => $this->group_id,
                'pilgrim_id' => $pilgrim->id,
            ]);
        }
    }
}
