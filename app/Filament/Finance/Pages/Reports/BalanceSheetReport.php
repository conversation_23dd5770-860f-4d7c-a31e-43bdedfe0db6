<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Enums\Finance\AccountCategory;
use App\Models\Currency;
use App\Models\Finance\ExchangeRate;
use App\Models\Finance\JournalEntryItem;
use App\Traits\CalculatesEarnings;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class BalanceSheetReport extends Page
{
    use CalculatesEarnings, InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Balance Sheet';

    protected static ?string $title = 'Balance Sheet Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 10;

    protected static string $view = 'filament.finance.pages.reports.balance-sheet-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('downloadPdf')
                ->disabled(fn () => $this->data === null)
                ->color('success'),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Get assets
            $assets = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->where('journal_entries.entry_date', '<=', $end)
                ->whereIn('cash_accounts.category', [
                    AccountCategory::CashBank->value,
                    AccountCategory::Inventory->value,
                    AccountCategory::CurrentAsset->value,
                    AccountCategory::AccountsReceivable->value,
                    AccountCategory::NonCurrentAsset->value,
                ])
                ->select([
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.category',
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
                ->orderBy('cash_accounts.code')
                ->get()
                ->map(function ($account) {
                    // For asset accounts (normal debit balance)
                    $balance = $account->total_debit - $account->total_credit;

                    return [
                        'code' => $account->code,
                        'name' => $account->name,
                        'category' => AccountCategory::from($account->category)->getLabel(),
                        'amount' => $balance,
                    ];
                })
                ->groupBy('category')
                ->toArray();

            // Get liabilities
            $liabilities = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->where('journal_entries.entry_date', '<=', $end)
                ->whereIn('cash_accounts.category', [
                    AccountCategory::CurrentLiability->value,
                    AccountCategory::AccountsPayable->value,
                    AccountCategory::NonCurrentLiability->value,
                ])
                ->select([
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.category',
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
                ->orderBy('cash_accounts.code')
                ->get()
                ->map(function ($account) {
                    // For liability accounts (normal credit balance)
                    $balance = $account->total_credit - $account->total_debit;

                    return [
                        'code' => $account->code,
                        'name' => $account->name,
                        'category' => AccountCategory::from($account->category)->getLabel(),
                        'amount' => $balance,
                    ];
                })
                ->groupBy('category')
                ->toArray();

            // Get equity
            $equity = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->where('journal_entries.entry_date', '<=', $end)
                ->where('cash_accounts.category', AccountCategory::Equity->value)
                ->select([
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.category',
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
                ->orderBy('cash_accounts.code')
                ->get()
                ->map(function ($account) {
                    // For equity accounts (normal credit balance)
                    $balance = $account->total_credit - $account->total_debit;

                    return [
                        'code' => $account->code,
                        'name' => $account->name,
                        'category' => AccountCategory::from($account->category)->getLabel(),
                        'amount' => $balance,
                    ];
                })
                ->groupBy('category')
                ->toArray();

            // Calculate retained earnings from previous periods
            $previousPeriodEarnings = $this->calculatePreviousPeriodEarnings($start);

            // Calculate current period net income
            $earnings = $this->calculateEarnings($start, $end);
            $netIncome = $earnings['net_income'];

            // Add previous period retained earnings to equity if it's not zero
            if ($previousPeriodEarnings != 0) {
                if (! isset($equity[AccountCategory::Equity->getLabel()])) {
                    $equity[AccountCategory::Equity->getLabel()] = [];
                }

                $equity[AccountCategory::Equity->getLabel()][] = [
                    'name' => 'Retained Earnings (Previous Periods)',
                    'category' => AccountCategory::Equity->getLabel(),
                    'amount' => $previousPeriodEarnings,
                ];
            }

            // Add current period net income to equity if it's not zero
            if ($netIncome != 0) {
                if (! isset($equity[AccountCategory::Equity->getLabel()])) {
                    $equity[AccountCategory::Equity->getLabel()] = [];
                }

                $equity[AccountCategory::Equity->getLabel()][] = [
                    'name' => 'Retained Earnings (Current Period)',
                    'category' => AccountCategory::Equity->getLabel(),
                    'amount' => $netIncome,
                ];
            }

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => ExchangeRate::getExchangeRate($end, 'IDR', $data['currency_code']),
                'assets' => $assets,
                'liabilities' => $liabilities,
                'equity' => $equity,
                'net_income' => $netIncome,
                'previous_period_earnings' => $previousPeriodEarnings,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'balance-sheet-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.balance-sheet-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }
}
