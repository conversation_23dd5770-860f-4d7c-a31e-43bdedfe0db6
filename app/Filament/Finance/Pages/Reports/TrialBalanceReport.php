<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Enums\Finance\AccountCategory;
use App\Models\Currency;
use App\Models\Finance\ExchangeRate;
use App\Models\Finance\JournalEntryItem;
use App\Traits\CalculatesEarnings;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class TrialBalanceReport extends Page
{
    use CalculatesEarnings, InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Trial Balance';

    protected static ?string $title = 'Trial Balance Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 60;

    protected static string $view = 'filament.finance.pages.reports.trial-balance-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('downloadPdf')
                ->disabled(fn () => $this->data === null)
                ->color('success'),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Get real accounts (assets, liabilities, equity) with balances up to end date
            $realAccounts = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->whereIn('cash_accounts.category', [
                    AccountCategory::CashBank->value,
                    AccountCategory::Inventory->value,
                    AccountCategory::CurrentAsset->value,
                    AccountCategory::AccountsReceivable->value,
                    AccountCategory::NonCurrentAsset->value,
                    AccountCategory::AccumulatedDepreciation->value,
                    AccountCategory::CurrentLiability->value,
                    AccountCategory::AccountsPayable->value,
                    AccountCategory::NonCurrentLiability->value,
                    AccountCategory::Equity->value,
                ])
                ->where('journal_entries.entry_date', '<=', $end)
                ->select([
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.category',
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
                ->orderBy('cash_accounts.code');

            // Get nominal accounts (revenue, expenses) with balances only for the selected period
            $nominalAccounts = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->whereIn('cash_accounts.category', [
                    AccountCategory::OperatingRevenue->value,
                    AccountCategory::CostOfGoodsSold->value,
                    AccountCategory::OperatingExpense->value,
                    AccountCategory::CostOfSales->value,
                    AccountCategory::NonOperatingExpense->value,
                    AccountCategory::UncategorizedRevenue->value,
                    AccountCategory::UncategorizedExpense->value,
                ])
                ->whereBetween('journal_entries.entry_date', [$start, $end])
                ->select([
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.category',
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
                ->orderBy('cash_accounts.code');

            // Combine real and nominal accounts
            $accounts = $realAccounts->union($nominalAccounts)->get()
                ->map(function ($account) {
                    $category = AccountCategory::from($account->category);

                    // Calculate balance based on normal balance type
                    if ($category->isNormalDebitBalance()) {
                        // For accounts with normal debit balance (assets, expenses)
                        $balance = $account->total_debit - $account->total_credit;
                    } else {
                        // For accounts with normal credit balance (liabilities, equity, revenue)
                        $balance = $account->total_credit - $account->total_debit;
                    }

                    return [
                        'code' => $account->code,
                        'name' => $account->name,
                        'category' => $account->category,
                        'balance' => $balance,
                        'debit' => $balance > 0 && $category->isNormalDebitBalance() ? abs($balance) :
                                  ($balance < 0 && ! $category->isNormalDebitBalance() ? abs($balance) : 0),
                        'credit' => $balance > 0 && ! $category->isNormalDebitBalance() ? abs($balance) :
                                   ($balance < 0 && $category->isNormalDebitBalance() ? abs($balance) : 0),
                    ];
                })
                ->toArray();

            // Calculate retained earnings from previous periods
            $previousPeriodEarnings = $this->calculatePreviousPeriodEarnings($start);

            // Get the retained earnings account code from config
            $retainedEarningsCode = config('finance.coa.retained_earnings');

            // Add retained earnings to the accounts array
            $accounts[] = [
                'code' => $retainedEarningsCode,
                'name' => 'Retained Earnings',
                'category' => AccountCategory::Equity->value,
                'balance' => $previousPeriodEarnings,
                'debit' => $previousPeriodEarnings < 0 ? abs($previousPeriodEarnings) : 0,
                'credit' => $previousPeriodEarnings > 0 ? $previousPeriodEarnings : 0,
            ];

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => ExchangeRate::getExchangeRate($end, 'IDR', $data['currency_code']),
                'accounts' => $accounts,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'trial-balance-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.trial-balance-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }
}
