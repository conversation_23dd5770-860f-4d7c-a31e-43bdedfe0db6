<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Enums\InvoiceStatus;
use App\Exports\Finance\InvoiceReportExport;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Finance\ExchangeRate;
use App\Models\Finance\Invoice;
use App\Models\Group;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class InvoiceReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Invoice Report';

    protected static ?string $title = 'Invoice Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 30;

    protected static string $view = 'filament.finance.pages.reports.invoice-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
                Select::make('customer_id')
                    ->label('Customer (Optional)')
                    ->options(Customer::pluck('name', 'id'))
                    ->searchable()
                    ->placeholder('All Customers'),
                Select::make('status')
                    ->label('Status (Optional)')
                    ->options([
                        InvoiceStatus::Unpaid->value => InvoiceStatus::Unpaid->getLabel(),
                        InvoiceStatus::PaidPartial->value => InvoiceStatus::PaidPartial->getLabel(),
                        InvoiceStatus::Paid->value => InvoiceStatus::Paid->getLabel(),
                        InvoiceStatus::Overdue->value => InvoiceStatus::Overdue->getLabel(),
                        InvoiceStatus::Cancelled->value => InvoiceStatus::Cancelled->getLabel(),
                    ])
                    ->multiple()
                    ->placeholder('All Statuses'),
                Select::make('group_by')
                    ->label('Group By')
                    ->options([
                        'month' => 'Month',
                        'customer' => 'Customer',
                        'status' => 'Status',
                    ])
                    ->default('month'),
            ])
            ->statePath('filters')
            ->columns(4);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\ActionGroup::make([
                Actions\Action::make('download_pdf')
                    ->label('PDF')
                    ->icon('heroicon-o-document-arrow-down')
                    ->action('downloadPdf')
                    ->disabled(fn () => $this->data === null),
                Actions\Action::make('download_xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action('downloadXlsx')
                    ->disabled(fn () => $this->data === null),
            ])
                ->label('Download')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->button(),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Build base query
            $invoiceQuery = Invoice::query()
                ->with(['customer', 'group', 'package', 'items.product', 'payments'])
                ->whereBetween('invoice_date', [$start, $end]);

            // Apply filters
            if (! empty($data['customer_id'])) {
                $invoiceQuery->where('customer_id', $data['customer_id']);
            }

            if (! empty($data['status'])) {
                $invoiceQuery->whereIn('status', $data['status']);
            }

            $invoices = $invoiceQuery->orderBy('invoice_date', 'desc')->get();

            $rates = [
                'SAR' => ExchangeRate::getExchangeRate($end, 'IDR', 'SAR'),
                'USD' => ExchangeRate::getExchangeRate($end, 'IDR', 'USD'),
            ];

            // Get exchange rate for currency conversion
            $exchangeRate = ExchangeRate::getExchangeRate($end, 'IDR', $data['currency_code']);

            // Calculate summary metrics
            $summary = $this->calculateSummaryMetrics($invoices, $rates, $exchangeRate);

            // Group data based on selection
            $groupedData = $this->groupInvoiceData($invoices, $data['group_by'], $start, $end, $rates, $exchangeRate);

            // Get detailed invoice list
            $invoiceList = $this->getInvoiceList($invoices, $rates, $exchangeRate);

            // Get aging analysis
            $agingAnalysis = $this->getAgingAnalysis($invoices, $rates, $exchangeRate);

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => $exchangeRate,
                'summary' => $summary,
                'grouped_data' => $groupedData,
                'invoice_list' => $invoiceList,
                'aging_analysis' => $agingAnalysis,
                'group_by' => $data['group_by'],
                'filters' => [
                    'customer' => $data['customer_id'] ? Customer::find($data['customer_id'])?->name : null,
                    'status' => $data['status'] ? collect($data['status'])->map(fn ($s) => InvoiceStatus::tryFrom($s)?->getLabel())->filter()->join(', ') : null,
                ],
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    private function calculateSummaryMetrics($invoices, $rates, $exchangeRate = 1): array
    {
        $totalInvoices = $invoices->count();
        $totalAmount = $invoices->sum(function ($invoice) use ($rates, $exchangeRate) {
            return $invoice->currency_code === 'IDR'
                ? $invoice->total * $exchangeRate
                : $invoice->total * $rates[$invoice->currency_code] * $exchangeRate;
        });
        $totalPaid = $invoices->sum(function ($invoice) use ($rates, $exchangeRate) {
            return $invoice->currency_code === 'IDR'
                ? $invoice->paid * $exchangeRate
                : $invoice->paid * $rates[$invoice->currency_code] * $exchangeRate;
        });
        $totalOutstanding = $totalAmount - $totalPaid;
        $averageInvoiceValue = $totalInvoices > 0 ? $totalAmount / $totalInvoices : 0;
        $paymentRate = $totalAmount > 0 ? ($totalPaid / $totalAmount) * 100 : 0;

        // Count by status
        $statusCounts = $invoices->groupBy('status')->map->count();

        return [
            'total_invoices' => $totalInvoices,
            'total_amount' => $totalAmount,
            'total_paid' => $totalPaid,
            'total_outstanding' => $totalOutstanding,
            'average_invoice_value' => $averageInvoiceValue,
            'payment_rate' => $paymentRate,
            'status_counts' => $statusCounts,
            'unique_customers' => $invoices->unique('customer_id')->count(),
            'unique_groups' => $invoices->whereNotNull('group_id')->unique('group_id')->count(),
        ];
    }

    private function groupInvoiceData($invoices, $groupBy, $start, $end, $rates, $exchangeRate = 1): array
    {
        switch ($groupBy) {
            case 'month':
                return $this->groupByMonth($invoices, $start, $end, $rates, $exchangeRate);
            case 'customer':
                return $this->groupByCustomer($invoices, $rates, $exchangeRate);
            case 'status':
                return $this->groupByStatus($invoices, $rates, $exchangeRate);
            default:
                return [];
        }
    }

    private function groupByMonth($invoices, $start, $end, $rates, $exchangeRate = 1): array
    {
        $months = [];
        $current = $start->copy()->startOfMonth();

        while ($current <= $end) {
            $monthLabel = $current->format('M Y');

            $monthInvoices = $invoices->filter(function ($invoice) use ($current) {
                return $invoice->invoice_date->format('Y-m') === $current->format('Y-m');
            });

            $amount = $monthInvoices->sum(function ($invoice) use ($rates, $exchangeRate) {
                return $invoice->currency_code === 'IDR'
                    ? $invoice->total * $exchangeRate
                    : $invoice->total * $rates[$invoice->currency_code] * $exchangeRate;
            });
            $paid = $monthInvoices->sum(function ($invoice) use ($rates, $exchangeRate) {
                return $invoice->currency_code === 'IDR'
                    ? $invoice->paid * $exchangeRate
                    : $invoice->paid * $rates[$invoice->currency_code] * $exchangeRate;
            });

            $months[] = [
                'label' => $monthLabel,
                'count' => $monthInvoices->count(),
                'amount' => $amount,
                'paid' => $paid,
                'outstanding' => $amount - $paid,
            ];

            $current->addMonth();
        }

        return $months;
    }

    private function groupByCustomer($invoices, $rates, $exchangeRate = 1): array
    {
        return $invoices->groupBy('customer_id')->map(function ($customerInvoices) use ($rates, $exchangeRate) {
            $customer = $customerInvoices->first()->customer;

            $amount = $customerInvoices->sum(function ($invoice) use ($rates, $exchangeRate) {
                return $invoice->currency_code === 'IDR'
                    ? $invoice->total * $exchangeRate
                    : $invoice->total * $rates[$invoice->currency_code] * $exchangeRate;
            });
            $paid = $customerInvoices->sum(function ($invoice) use ($rates, $exchangeRate) {
                return $invoice->currency_code === 'IDR'
                    ? $invoice->paid * $exchangeRate
                    : $invoice->paid * $rates[$invoice->currency_code] * $exchangeRate;
            });

            return [
                'label' => $customer?->name ?? 'Unknown Customer',
                'count' => $customerInvoices->count(),
                'amount' => $amount,
                'paid' => $paid,
                'outstanding' => $amount - $paid,
            ];
        })->sortByDesc('amount')->values()->toArray();
    }

    private function groupByStatus($invoices, $rates, $exchangeRate = 1): array
    {
        return $invoices->groupBy('status')->map(function ($statusInvoices, $status) use ($rates, $exchangeRate) {
            $amount = $statusInvoices->sum(function ($invoice) use ($rates, $exchangeRate) {
                return $invoice->currency_code === 'IDR'
                    ? $invoice->total * $exchangeRate
                    : $invoice->total * $rates[$invoice->currency_code] * $exchangeRate;
            });
            $paid = $statusInvoices->sum(function ($invoice) use ($rates, $exchangeRate) {
                return $invoice->currency_code === 'IDR'
                    ? $invoice->paid * $exchangeRate
                    : $invoice->paid * $rates[$invoice->currency_code] * $exchangeRate;
            });

            return [
                'label' => $status instanceof InvoiceStatus ? $status->getLabel() : InvoiceStatus::tryFrom($status)?->getLabel() ?? '-',
                'count' => $statusInvoices->count(),
                'amount' => $amount,
                'paid' => $paid,
                'outstanding' => $amount - $paid,
            ];
        })->values()->toArray();
    }

    private function getInvoiceList($invoices, $rates, $exchangeRate = 1): array
    {
        return $invoices->map(function ($invoice) use ($rates, $exchangeRate) {
            $amount = $invoice->currency_code === 'IDR'
                ? $invoice->total * $exchangeRate
                : $invoice->total * $rates[$invoice->currency_code] * $exchangeRate;
            $paid = $invoice->currency_code === 'IDR'
                ? $invoice->paid * $exchangeRate
                : $invoice->paid * $rates[$invoice->currency_code] * $exchangeRate;

            return [
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'invoice_date' => $invoice->invoice_date,
                'due_date' => $invoice->due_date,
                'customer_name' => $invoice->customer?->name ?? 'Unknown',
                'group_name' => $invoice->group?->name ?? '-',
                'subject' => $invoice->subject,
                'amount' => $invoice->total,
                'paid' => $invoice->paid,
                'outstanding' => $invoice->total - $invoice->paid,
                'amount_idr' => $amount,
                'paid_idr' => $paid,
                'outstanding_idr' => $amount - $paid,
                'status' => $invoice->status,
                'currency_code' => $invoice->currency_code,
                'days_overdue' => $invoice->status === InvoiceStatus::Overdue
                    ? round(now()->diffInDays($invoice->due_date))
                    : 0,
            ];
        })->toArray();
    }

    private function getAgingAnalysis($invoices, $rates, $exchangeRate = 1): array
    {
        $unpaidInvoices = $invoices->filter(function ($invoice) {
            return in_array($invoice->status, [InvoiceStatus::Unpaid, InvoiceStatus::PaidPartial, InvoiceStatus::Overdue]);
        });

        $aging = [
            'current' => 0,      // 0-30 days
            'days_31_60' => 0,   // 31-60 days
            'days_61_90' => 0,   // 61-90 days
            'over_90' => 0,      // Over 90 days
        ];

        foreach ($unpaidInvoices as $invoice) {
            $outstanding = $invoice->currency_code === 'IDR'
                ? ($invoice->total - $invoice->paid) * $exchangeRate
                : ($invoice->total - $invoice->paid) * $rates[$invoice->currency_code] * $exchangeRate;

            $daysOverdue = now()->diffInDays($invoice->due_date);

            if ($daysOverdue <= 30) {
                $aging['current'] += $outstanding;
            } elseif ($daysOverdue <= 60) {
                $aging['days_31_60'] += $outstanding;
            } elseif ($daysOverdue <= 90) {
                $aging['days_61_90'] += $outstanding;
            } else {
                $aging['over_90'] += $outstanding;
            }
        }

        return $aging;
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'invoice-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.invoice-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }

    public function downloadXlsx()
    {
        if ($this->data === null) {
            return;
        }

        $fileName = 'invoice-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.xlsx';

        return (new InvoiceReportExport($this->data, $this->period))->download($fileName);
    }
}
