<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Exports\Finance\AdvanceCashExport;
use App\Models\Bill;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\ExchangeRate;
use App\Models\Finance\JournalEntryItem;
use App\Models\Finance\UserCash;
use App\Models\User;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class AdvanceCashReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Advance Cash Report';

    protected static ?string $title = 'Advance Cash Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 75;

    protected static string $view = 'filament.finance.pages.reports.advance-cash-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
                Select::make('user_id')
                    ->label('User (Optional)')
                    ->placeholder('All Users')
                    ->options(fn () => User::query()
                        ->whereHas('userCashes')
                        ->orderBy('name')
                        ->get()
                        ->mapWithKeys(function ($user) {
                            return [$user->id => $user->name];
                        })
                    )
                    ->searchable(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\ActionGroup::make([
                Actions\Action::make('download_pdf')
                    ->label('PDF')
                    ->icon('heroicon-o-document-arrow-down')
                    ->action('downloadPdf')
                    ->disabled(fn () => $this->data === null),
                Actions\Action::make('download_xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action('downloadXlsx')
                    ->disabled(fn () => $this->data === null),
            ])
                ->label('Download')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->button(),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // First, get all users who have transactions in the date range
            $transactionQuery = JournalEntryItem::query()
                ->with(['entry.transaction'])
                ->join('users', 'users.id', '=', 'journal_entry_items.owner_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->whereBetween('journal_entries.entry_date', [$start, $end])
                ->whereNotNull('owner_id')
                ->where('account_id', CashAccount::getIdForCode(config('finance.coa.advance_cash')));

            // Filter by specific user if selected
            if (! empty($data['user_id'])) {
                $transactionQuery->where('owner_id', $data['user_id']);
            }

            // Get all transactions ordered by user name and cashed_at date
            $transactions = $transactionQuery
                ->select([
                    'journal_entry_items.*',
                    'users.id as user_id',
                    'users.name as user_name',
                    'journal_entries.entry_date',
                    'journal_entries.details',
                    DB::raw('amount * exchange_rate as amount'),
                ])
                ->orderBy('users.name')
                ->orderBy('journal_entries.entry_date')
                ->orderBy('journal_entries.transaction_id')
                ->get();

            // Group transactions by user
            $usersData = [];
            foreach ($transactions as $transaction) {
                $userId = $transaction->user_id;

                if (! isset($usersData[$userId])) {
                    $usersData[$userId] = [
                        'user' => [
                            'id' => $transaction->user_id,
                            'name' => $transaction->user_name,
                        ],
                        'transactions' => [],
                        'opening_balance' => 0,
                        'closing_balance' => 0,
                    ];
                }

                // $refNumber = '';
                // if ($transaction->entry->transaction?->related && $transaction->entry->transaction->related instanceof Bill) {
                //     $refNumber = $transaction->entry->transaction->related->bill_number;
                // } else {
                $refNumber = $transaction->entry->transaction?->getTransactionNumber();
                // }

                $usersData[$userId]['transactions'][] = [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->entry->entry_date,
                    'details' => $transaction->entry->details,
                    'ref_number' => $refNumber,
                    'type' => $transaction->type,
                    'debit' => $transaction->type === 'd' ? $transaction->amount : 0,
                    'credit' => $transaction->type === 'c' ? $transaction->amount : 0,
                    'amount' => $transaction->amount,
                ];
            }

            // Now find users with non-zero balances who don't have transactions in the period
            $this->addUsersWithNonZeroBalances($usersData, $start, $data['user_id'] ?? null);

            // Calculate opening balances and running balances
            $this->calculateBalances($usersData, $start);

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => ExchangeRate::getExchangeRate($end, 'IDR', $data['currency_code']),
                'users' => array_values($usersData),
                'selected_user_id' => $data['user_id'] ?? null,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    private function addUsersWithNonZeroBalances(array &$usersData, Carbon $start, ?int $selectedUserId): void
    {
        // Get all users who have UserCash transactions and calculate their balance up to the start date
        $balanceQuery = JournalEntryItem::query()
            ->join('users', 'users.id', '=', 'journal_entry_items.owner_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->where('journal_entries.entry_date', '<', $start)
            ->where('account_id', CashAccount::getIdForCode(config('finance.coa.advance_cash')))
            ->whereNotNull('owner_id')
            ->select([
                'users.id as user_id',
                'users.name as user_name',
                DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
            ])
            ->groupBy('users.id', 'users.name');

        // Filter by specific user if selected
        if ($selectedUserId) {
            $balanceQuery->where('owner_id', $selectedUserId);
        }

        $userBalances = $balanceQuery->get();

        foreach ($userBalances as $userBalance) {
            $balance = ($userBalance->total_debit ?? 0) - ($userBalance->total_credit ?? 0);

            // Only include users with non-zero balance who are not already in the usersData
            if ($balance != 0 && ! isset($usersData[$userBalance->user_id])) {
                $usersData[$userBalance->user_id] = [
                    'user' => [
                        'id' => $userBalance->user_id,
                        'name' => $userBalance->user_name,
                    ],
                    'transactions' => [],
                    'opening_balance' => 0,
                    'closing_balance' => 0,
                ];
            }
        }
    }

    private function calculateBalances(array &$usersData, Carbon $start): void
    {
        foreach ($usersData as $userId => &$userData) {
            // Calculate opening balance (transactions before the start date)
            $openingBalance = JournalEntryItem::query()
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->where('journal_entries.entry_date', '<', $start)
                ->where('account_id', CashAccount::getIdForCode(config('finance.coa.advance_cash')))
                ->where('owner_id', $userId)
                ->select([
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->first();

            // For advance cash: debit increases balance (cash given to user), credit decreases balance (cash returned/spent)
            $userData['opening_balance'] = ($openingBalance->total_debit ?? 0) - ($openingBalance->total_credit ?? 0);

            // Calculate running balances for each transaction
            $runningBalance = $userData['opening_balance'];
            foreach ($userData['transactions'] as &$transaction) {
                // Debit increases user's advance cash balance, credit decreases it
                $runningBalance += $transaction['debit'] - $transaction['credit'];
                $transaction['balance'] = $runningBalance;
            }

            $userData['closing_balance'] = $runningBalance;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'advance-cash-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.advance-cash-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }

    public function downloadXlsx()
    {
        if ($this->data === null) {
            return;
        }

        $fileName = 'advance-cash-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.xlsx';

        return (new AdvanceCashExport($this->data, $this->period))->download($fileName);
    }
}
