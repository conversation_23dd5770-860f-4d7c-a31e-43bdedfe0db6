<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\GroupFinanceResource\Pages;
use App\Filament\Resources\GroupResource;
use App\Models\Group;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Umrahservice\Groups\Enums\GroupProgress;
use Umrahservice\Groups\Enums\GroupStatus;

class GroupFinanceResource extends Resource
{
    protected static ?string $model = Group::class;

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 1;

    protected static bool $shouldIgnorePolicies = true;

    protected static ?string $recordTitleAttribute = 'name';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getRouteBaseName(?string $panel = null): string
    {
        return parent::getRouteBaseName('finance');
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['customer.name', 'name', 'number'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        /** @var Group $record */
        return [
            'Customer' => $record->customer->name,
        ];
    }

    public static function getGlobalSearchResultUrl(Model $record): ?string
    {
        return static::getUrl('closing-report', ['record' => $record]);
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()
            ->with(['customer'])
            ->currentPeriod();
    }

    public static function getRecordTitle(?Model $record): ?string
    {
        /** @var Group|null $record */
        return $record?->full_name ?? 'Untitled';
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query
                ->with(['customer', 'invoices'])
                ->currentPeriod())
            ->recordClasses(fn ($record) => match ($record->status) {
                GroupStatus::Draft => 'opacity-90',
                GroupStatus::Cancelled => 'bg-danger-500/10',
                default => null,
            })
            ->groups([
                Tables\Grouping\Group::make('customer.name')
                    ->titlePrefixedWithLabel(false),
                Tables\Grouping\Group::make('arrival_date')
                    ->date(),
            ])
            ->columns([
                Tables\Columns\TextColumn::make('invoice')
                    ->label('Invoice')
                    ->money('SAR')
                    ->getStateUsing(fn ($record) => filled($record->invoices) ? $record->invoices->sum(fn ($i) => $i->total * $i->exchange_rate) : null)
                    ->description(fn ($record) => $record->invoices ? $record->invoices->pluck('invoice_number')->implode(', ') : null, 'above')
                    ->visible(fn () => auth('web')->user()->hasRole(['Admin', 'Finance'])),
                BadgeableColumn::make('group')
                    ->label('Group')
                    ->getStateUsing(fn ($record) => $record->customer->name)
                    ->description(fn ($record) => $record->name)
                    ->searchable(['name'])
                    ->suffixBadges([
                        Badge::make('pax')
                            ->label(fn ($record) => $record->total_pax . ' pax'),
                        Badge::make('status')
                            ->color(fn ($record) => $record->status->getColor())
                            ->label(fn ($record) => $record->status->getLabel())
                            ->visible(fn ($record) => $record->status != GroupStatus::Confirmed),
                        Badge::make('progress')
                            ->color(fn ($record) => $record->progress->getColor())
                            ->label(fn ($record) => $record->progress->getLabel())
                            ->visible(fn ($record) => $record->progress != GroupProgress::Waiting),
                    ]),
                Tables\Columns\TextColumn::make('arrival_date')
                    ->label('Arrival')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('departure_date')
                    ->label('Departure')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date Created')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(GroupStatus::class),
                Tables\Filters\SelectFilter::make('customer')
                    ->relationship('customer', 'name')
                    ->searchable(),
                Tables\Filters\SelectFilter::make('services')
                    ->options(Group::GROUP_SERVICES)
                    ->multiple()
                    ->query(function ($query, $data) {
                        if (filled($data['values'])) {
                            $query->where(function ($query) use ($data) {
                                collect($data['values'])
                                    ->each(function ($value) use ($query) {
                                        $query
                                            ->where('services', 'like', '%' . $value . '%');
                                    });
                            });
                        }

                        return $query;
                    }),
                DateRangeFilter::make('arrival_date')
                    ->indicateUsing(function ($data) {
                        $datesString = data_get($data, 'arrival_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Arrival date: {$datesString}";
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('expenses')
                        ->icon('heroicon-m-banknotes')
                        ->color('warning')
                        ->url(fn ($record) => self::getUrl('expenses', ['record' => $record])),
                    Tables\Actions\Action::make('closing_report')
                        ->icon('heroicon-m-document-text')
                        ->color('gray')
                        ->url(fn ($record) => self::getUrl('closing-report', ['record' => $record])),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('view_pif')
                            ->label('View PIF')
                            ->icon('heroicon-m-document')
                            ->color('gray')
                            ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record])),
                    ])->dropdown(false),
                ]),
            ])
            ->defaultSort('groups.created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageGroupFinances::route('/'),
            'expenses' => Pages\Expenses::route('/{record}/expenses'),
            'closing-report' => Pages\ClosingReport::route('/{record}/closing-report'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\Expenses::class,
            Pages\ClosingReport::class,
        ]);
    }
}
