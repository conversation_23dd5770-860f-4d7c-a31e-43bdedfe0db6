<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\CreditNoteResource\Pages;
use App\Models\Customer;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CreditNoteResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationLabel = 'Credit Notes';

    protected static ?string $modelLabel = 'credit note';

    protected static ?string $pluralModelLabel = 'Credit Notes';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 70;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withSum([
                'creditNotes as total_credit_balance' => fn ($query) => $query->whereNull('used_for_invoice_id'),
            ], DB::raw('amount * exchange_rate'))
            ->withCount([
                'creditNotes as unused_credit_count' => fn ($query) => $query->whereNull('used_for_invoice_id'),
            ])
            ->withMin('creditNotes as oldest_credit_date', 'invoice_date')
            ->with(
                'creditNotes',
                fn ($query) => $query->with('invoice', fn ($query) => $query->whereNull('used_for_invoice_id'))
            )
            ->having('total_credit_balance', '>', 0);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // No form needed - this is read-only
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Customer')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => collect([$record->email, $record->phone])->filter()->join(', ')),

                Tables\Columns\TextColumn::make('total_credit_balance')
                    ->label('Balance')
                    ->sortable()
                    ->currencyRight(),

                Tables\Columns\TextColumn::make('unused_credit_count')
                    ->label('Count')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('creditNotes.invoice.invoice_number')
                    ->label('Invoices')
                    ->limit(50)
                    ->listWithLineBreaks(),

                Tables\Columns\TextColumn::make('oldest_credit_date')
                    ->label('Oldest Credit')
                    ->date()
                    ->sortable()
                    ->color('gray'),
            ])
            ->defaultSort('total_credit_balance', 'desc')
            ->striped();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCreditNotes::route('/'),
        ];
    }
}
