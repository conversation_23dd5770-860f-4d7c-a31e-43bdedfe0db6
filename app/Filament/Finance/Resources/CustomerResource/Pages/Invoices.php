<?php

namespace App\Filament\Finance\Resources\CustomerResource\Pages;

use App\Enums\InvoiceStatus;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\CustomerResource;
use App\Filament\Finance\Resources\CustomerResource\Widgets;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Models\Finance\Invoice;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Invoices extends ManageRelatedRecords
{
    protected static string $resource = CustomerResource::class;

    protected static string $relationship = 'invoices';

    protected static ?string $navigationIcon = 'heroicon-o-document-duplicate';

    public static function getNavigationLabel(): string
    {
        return 'Invoices';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('statement')
                ->label('View Statement')
                ->url(route('customers.statements', ['customerUuid' => $this->record->uuid]))
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->openUrlInNewTab(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\CustomerInvoiceOverview::class,
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('invoice_number')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('invoice_number')
            ->modifyQueryUsing(fn ($query) => $query->with(['group'])
                ->select([
                    'invoices.*',
                    DB::raw('invoices.total - invoices.paid AS balance'),
                ]))
            ->recordClasses(fn ($record) => match ($record->status) {
                InvoiceStatus::Paid => 'bg-success-500/10',
                InvoiceStatus::Overdue => 'bg-danger-500/10',
                InvoiceStatus::Cancelled => 'bg-gray-500/10',
                InvoiceStatus::Unpaid => 'bg-warning-500/10',
                default => null
            })
            ->columns([
                Tables\Columns\TextColumn::make('invoice_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('group.name')
                    ->searchable()
                    ->sortable()
                    ->default(fn ($record) => 'Group #' . $record->group_id)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('invoice_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->counts('items')
                    ->label('Items')
                    ->badge(),
                Tables\Columns\TextColumn::make('total')
                    ->label('Total invoice')
                    ->sortable()
                    ->currencyAuto(),
                Tables\Columns\TextColumn::make('paid')
                    ->label('Total paid')
                    ->sortable()
                    ->currencyAuto()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('balance')
                    ->sortable()
                    ->currencyAuto()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge(),
                Tables\Columns\CheckboxColumn::make('is_published')
                    ->label('Published'),
            ])
            ->filters([
                Invoice::getDateTableFilter(),
                Tables\Filters\SelectFilter::make('status')
                    ->options(InvoiceStatus::class)
                    ->multiple(),
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Invoice::getSendTableAction(),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\ViewAction::make(),
                        Tables\Actions\EditAction::make(),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('cancel')
                            ->label('Cancel')
                            ->icon('heroicon-m-x-mark')
                            ->color('danger')
                            ->action(function ($record, $data) {
                                $record->update([
                                    ...$data,
                                    'status' => InvoiceStatus::Cancelled,
                                ]);
                            })
                            ->modalHeading('Cancel Invoice')
                            ->form([
                                Forms\Components\Textarea::make('cancellation_note')
                                    ->required(),
                            ])
                            ->requiresConfirmation()
                            ->visible(fn ($record) => $record->status !== InvoiceStatus::Cancelled),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('payments')
                            ->icon('heroicon-m-banknotes')
                            ->url(fn ($record) => InvoiceResource::getUrl('payments', ['record' => $record])),
                        Tables\Actions\Action::make('refunds')
                            ->icon('heroicon-m-receipt-refund')
                            ->url(fn ($record) => InvoiceResource::getUrl('refunds', ['record' => $record]))
                            ->visible(fn ($record) => $record->status === InvoiceStatus::Cancelled),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ]);
    }

    protected function configureViewAction(Tables\Actions\ViewAction $action): void
    {
        $action
            ->authorize(static fn (ManageRelatedRecords $livewire, Model $record): bool => $livewire->canView($record))
            ->url(fn ($record) => InvoiceResource::getUrl('view', ['record' => $record]));
    }

    protected function configureEditAction(Tables\Actions\EditAction $action): void
    {
        $action
            ->authorize(static fn (ManageRelatedRecords $livewire, Model $record): bool => $livewire->canEdit($record))
            ->url(fn ($record) => InvoiceResource::getUrl('edit', ['record' => $record]));
    }
}
