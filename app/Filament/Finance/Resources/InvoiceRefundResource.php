<?php

namespace App\Filament\Finance\Resources;

use App\Enums\Finance\AccountCategory;
use App\Enums\PaymentMethod;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\InvoiceRefundResource\Pages;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoiceRefund;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InvoiceRefundResource extends Resource
{
    protected static ?string $model = InvoiceRefund::class;

    protected static ?string $navigationLabel = 'Refunds';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 35;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Details')
                            ->columns()
                            ->schema([
                                Forms\Components\Select::make('invoice_id')
                                    ->label('Invoice')
                                    ->relationship('invoice', 'invoice_number')
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $invoice = Invoice::find($state);
                                        $set('currency_code', $invoice?->currency_code ?? 'SAR');
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($invoice?->currency_code ?? 'SAR'));
                                    }),
                                Forms\Components\ToggleButtons::make('payment_method')
                                    ->grouped()
                                    ->options(PaymentMethod::class)
                                    ->default(PaymentMethod::Cash->value)
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        if ($state === 'deposit') {
                                            $set('cash_account_id', CashAccount::query()
                                                ->where('code', config('finance.coa.customer_deposit'))
                                                ->first()->id);
                                        } elseif ($state === 'accounts_payable') {
                                            $set('cash_account_id', CashAccount::query()
                                                ->where('category', AccountCategory::AccountsPayable)
                                                ->first()->id);
                                        } else {
                                            $set('cash_account_id', null);
                                        }
                                    }),
                                Forms\Components\Select::make('cash_account_id')
                                    ->label('Account')
                                    ->options(function ($get) {
                                        $categories = $get('payment_method') === 'accounts_payable'
                                            ? [AccountCategory::AccountsPayable]
                                            : [AccountCategory::CashBank];

                                        return CashAccount::query()
                                            ->when(
                                                $get('payment_method') === 'deposit',
                                                fn ($query) => $query->where('code', config('finance.coa.customer_deposit')),
                                                fn ($query) => $query->whereIn('category', $categories)
                                            )
                                            ->orderBy('code')
                                            ->get()
                                            ->mapWithKeys(function ($account) {
                                                return [$account->id => $account->fullname];
                                            });
                                    })
                                    ->required()
                                    ->searchable()
                                    ->disabled(fn ($get) => $get('payment_method') === 'deposit')
                                    ->dehydrated(),
                                Forms\Components\DateTimePicker::make('refunded_at')
                                    ->label('Date')
                                    ->default(today())
                                    ->required(),
                                Forms\Components\TextInput::make('description')
                                    ->autocomplete('off')
                                    ->maxLength(255),

                                Forms\Components\ToggleButtons::make('currency_code')
                                    ->label('Currency')
                                    ->default('SAR')
                                    ->options(Currency::getOptions())
                                    ->inline()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),

                                Forms\Components\TextInput::make('amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR'),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->default(1)
                                    ->live(onBlur: true)
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->afterStateUpdated(function ($set, $state) {
                                        $set('exchange_rate', evaluate_math_string($state));
                                    })
                                    ->disabled(fn ($get) => $get('currency_code') === config('finance.base_currency'))
                                    ->dehydrated(),

                                Forms\Components\FileUpload::make('attachment')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('invoice/refunds')
                                    ->visibility('public'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Memo')
                            ->schema([
                                Forms\Components\Textarea::make('memo')
                                    ->hiddenLabel(),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['invoice', 'cash_account']))
            ->columns([
                Tables\Columns\TextColumn::make('invoice.invoice_number')
                    ->label('Invoice'),
                Tables\Columns\TextColumn::make('id')
                    ->label('No.'),
                Tables\Columns\TextColumn::make('refunded_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Via')
                    ->badge(),
                Tables\Columns\TextColumn::make('cash_account.name')
                    ->label('Account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->defaultSort('refunded_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageInvoiceRefunds::route('/'),
        ];
    }
}
