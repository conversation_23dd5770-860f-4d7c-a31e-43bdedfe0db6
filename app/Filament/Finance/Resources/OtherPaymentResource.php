<?php

namespace App\Filament\Finance\Resources;

use App\Enums\CashTransactionType;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\OtherPaymentResource\Pages;
use App\Filament\Resources\GroupResource;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\CashTransaction;
use App\Models\Group;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class OtherPaymentResource extends Resource
{
    protected static ?string $model = CashTransaction::class;

    protected static ?string $modelLabel = 'other payment';

    protected static ?string $navigationGroup = 'Cash & Bank';

    protected static ?int $navigationSort = 0;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('type', CashTransactionType::Out);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Details')
                            ->columns()
                            ->schema([
                                Forms\Components\Hidden::make('type')
                                    ->default('out'),
                                Forms\Components\DatePicker::make('transaction_date')
                                    ->default(now())
                                    ->required(),
                                Group::formFieldSelectGroup(),
                                Forms\Components\TextInput::make('details')
                                    ->columnSpanFull(),
                                Forms\Components\Select::make('credit_account_id')
                                    ->label('Cash/Bank Account')
                                    ->options(fn () => CashAccount::query()
                                        ->isCashOrBank()
                                        ->orderBy('code')
                                        ->get()
                                        ->mapWithKeys(function ($account) {
                                            return [$account->id => $account->fullname];
                                        })
                                    )
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('debit_account_id')
                                    ->label('Expense Account')
                                    ->options(fn () => CashAccount::query()
                                        ->isNotCashOrBank()
                                        ->orderBy('code')
                                        ->get()
                                        ->mapWithKeys(function ($account) {
                                            return [$account->id => $account->fullname];
                                        })
                                    )
                                    ->required()
                                    ->searchable(),
                                Forms\Components\ToggleButtons::make('currency_code')
                                    ->label('Currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->grouped()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),
                                Forms\Components\TextInput::make('amount')
                                    ->numeric()
                                    ->prefix(fn ($get) => $get('currency_code'))
                                    ->required(),
                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->default(1)
                                    ->live(onBlur: true)
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->afterStateUpdated(function ($set, $state) {
                                        $set('exchange_rate', evaluate_math_string($state));
                                    })
                                    ->disabled(fn ($get) => $get('currency_code') === 'SAR')
                                    ->dehydrated(),
                            ]),
                        Forms\Components\Tabs\Tab::make('Attachments')
                            ->schema([
                                Forms\Components\FileUpload::make('attachments')
                                    ->hiddenLabel()
                                    ->multiple()
                                    ->panelLayout('grid')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('finance/attachments')
                                    ->visibility('public'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Memo')
                            ->schema([
                                Forms\Components\Textarea::make('memo')
                                    ->hiddenLabel(),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['group.customer', 'debit_account', 'credit_account']))
            ->columns([
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn ($record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer?->name)
                    ->description(fn ($record) => $record->group?->name),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('credit_account.name')
                    ->label('Cash/Bank'),
                Tables\Columns\TextColumn::make('debit_account.name')
                    ->label('Expense'),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachments(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Cash/Bank')
                    ->native(false)
                    ->options(fn () => CashAccount::query()
                        ->isCashOrBank()
                        ->orderBy('code')
                        ->get()
                        ->mapWithKeys(function ($account) {
                            return [$account->id => $account->fullname];
                        })
                    )
                    ->modifyQueryUsing(fn ($query, $data) => $query
                        ->when($data['value'] ?? null, fn ($query, $value) => $query
                            ->where('credit_account_id', $value))),
                DateRangeFilter::make('transaction_date')
                    ->label('Date range')
                    ->withIndicator()
                    ->indicateUsing(function ($data, $filter) {
                        $datesString = data_get($data, 'transaction_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Date range: {$datesString}";
                    })
                    ->columnSpan(2),
                Group::tableFilterGroup(),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOtherPayments::route('/'),
        ];
    }
}
