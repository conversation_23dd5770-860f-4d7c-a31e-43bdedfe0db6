<?php

namespace App\Filament\Finance\Resources;

use App\Exports\CustomerDepositExport;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\CustomerDepositResource\Pages;
use App\Filament\Finance\Resources\CustomerDepositResource\Widgets;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\CustomerCash;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Facades\Excel;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class CustomerDepositResource extends Resource
{
    protected static ?string $model = CustomerCash::class;

    protected static ?string $modelLabel = 'transaction';

    protected static ?string $navigationLabel = 'Deposits';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 100;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Details')
                            ->columns()
                            ->schema([
                                Forms\Components\Select::make('customer_id')
                                    ->label('Customer')
                                    ->relationship('customer', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Forms\Components\DateTimePicker::make('cashed_at')
                                    ->label('Date')
                                    ->default(now())
                                    ->required(),
                                Forms\Components\Select::make('cash_account_id')
                                    ->label('Cash account')
                                    ->relationship('cashAccount', 'name', fn ($query) => $query->orderBy('code'))
                                    ->searchable()
                                    ->preload()
                                    ->default(fn () => CashAccount::query()->where('code', config('finance.coa.main_cash'))->value('id')),
                                Forms\Components\TextInput::make('details')
                                    ->autocomplete('off')
                                    ->maxLength(255)
                                    ->required(),
                                Forms\Components\ToggleButtons::make('type')
                                    ->options([
                                        'd' => 'Debit',
                                        'c' => 'Credit',
                                    ])
                                    ->grouped()
                                    ->default('d')
                                    ->live(),
                                Forms\Components\ToggleButtons::make('currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->grouped()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),
                                Forms\Components\TextInput::make('amount')
                                    ->numeric()
                                    ->prefix(fn ($get) => $get('currency'))
                                    ->required(),
                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->default(1)
                                    ->live(onBlur: true)
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->afterStateUpdated(function ($set, $state) {
                                        $set('exchange_rate', evaluate_math_string($state));
                                    })
                                    ->disabled(fn ($get) => $get('currency_code') === config('finance.base_currency'))
                                    ->dehydrated(),
                                Forms\Components\FileUpload::make('attachment')
                                    ->label('Bukti Transaksi')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('finance/attachments')
                                    ->visibility('public'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Memo')
                            ->schema([
                                Forms\Components\Textarea::make('memo')
                                    ->hiddenLabel(),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        Model::preventLazyLoading(false);

        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['related']))
            ->headerActions([
                Tables\Actions\Action::make('download')
                    ->label('Download Statement')
                    ->icon('heroicon-o-document-arrow-down')
                    ->action(function ($livewire) {
                        return Excel::download(
                            new CustomerDepositExport($livewire->getFilteredTableQuery()),
                            'customer_deposits_' . now()->format('Y-m-d_His') . '.xlsx'
                        );
                    }),
            ])
            ->columns([
                Tables\Columns\TextColumn::make('customer.name')
                    ->url(fn ($record) => static::getUrl('index', ['tableFilters[customer_id][value]' => $record->customer_id])),
                Tables\Columns\TextColumn::make('cashed_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice')
                    ->formatStateUsing(fn ($state) => $state->invoice_number)
                    ->url(fn ($state) => $state ? InvoiceResource::getUrl('view', ['record' => $state]) : null),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->description(fn ($record) => $record->related_details, 'above')
                    ->searchable(),
                Tables\Columns\TextColumn::make('debit')
                    ->getStateUsing(fn ($record) => $record->type == 'd'
                    ? $record->amount * $record->exchange_rate
                    : null)
                    ->description(function ($record) {
                        return $record->type == 'd' && $record->currency != 'SAR'
                            ? money($record->amount, $record->currency, true)
                            : null;
                    })
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('credit')
                    ->getStateUsing(fn ($record) => $record->type == 'c'
                    ? $record->amount * $record->exchange_rate
                    : null)
                    ->description(function ($record) {
                        return $record->type == 'c' && $record->currency != 'SAR'
                            ? money($record->amount, $record->currency, true)
                            : null;
                    })
                    ->currencyRight(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
            ])
            ->filters([
                DateRangeFilter::make('cashed_at')
                    ->label('Date range')
                    ->withIndicator(),
                Tables\Filters\SelectFilter::make('customer_id')
                    ->label('Customer')
                    ->relationship('customer', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    CustomerCash::getReceiptTableAction(),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\EditAction::make()
                            ->visible(fn ($record) => ! $record->related_id),
                        Tables\Actions\DeleteAction::make()
                            ->visible(fn ($record) => ! $record->related_id),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('download_statement')
                        ->label('Download Statement')
                        ->icon('heroicon-o-document-arrow-down')
                        ->action(function ($records) {
                            return Excel::download(
                                new CustomerDepositExport($records->toQuery()),
                                'customer_deposits_' . now()->format('Y-m-d_His') . '.xlsx'
                            );
                        }),
                ]),
            ])
            ->defaultSort('cashed_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCustomerDeposits::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\DepositOverview::class,
        ];
    }
}
