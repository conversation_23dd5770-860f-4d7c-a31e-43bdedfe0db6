<?php

namespace App\Filament\Finance\Resources\CreditNoteResource\Pages;

use App\Filament\Finance\Resources\CreditNoteResource;
use Filament\Resources\Pages\ManageRecords;

class ManageCreditNotes extends ManageRecords
{
    protected static string $resource = CreditNoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action needed since this is read-only
        ];
    }

    public function getTitle(): string
    {
        return 'Credit Notes';
    }
}
