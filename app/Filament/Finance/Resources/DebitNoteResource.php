<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\DebitNoteResource\Pages;
use App\Models\Vendor;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class DebitNoteResource extends Resource
{
    protected static ?string $model = Vendor::class;

    protected static ?string $navigationLabel = 'Debit Notes';

    protected static ?string $modelLabel = 'debit note';

    protected static ?string $pluralModelLabel = 'Debit Notes';

    protected static ?string $navigationGroup = 'Purchases';

    protected static ?int $navigationSort = 70;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withSum([
                'debitNotes as total_debit_balance' => fn ($query) => $query->whereNull('used_for_bill_id'),
            ], DB::raw('amount * exchange_rate'))
            ->withCount([
                'debitNotes as unused_debit_count' => fn ($query) => $query->whereNull('used_for_bill_id'),
            ])
            ->withMin('debitNotes as oldest_debit_date', 'bill_date')
            ->with(
                'debitNotes',
                fn ($query) => $query->with('bill', fn ($query) => $query->whereNull('used_for_bill_id'))
            )
            ->having('total_debit_balance', '>', 0);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // No form needed - this is read-only
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('company_name')
                    ->label('Vendor')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => collect([$record->contact_email, $record->contact_phone])->filter()->join(', ')),

                Tables\Columns\TextColumn::make('total_debit_balance')
                    ->label('Balance')
                    ->sortable()
                    ->currencyRight(),

                Tables\Columns\TextColumn::make('unused_debit_count')
                    ->label('Count')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('debitNotes.bill.bill_number')
                    ->label('Bills')
                    ->limit(50)
                    ->listWithLineBreaks(),

                Tables\Columns\TextColumn::make('oldest_debit_date')
                    ->label('Oldest Debit')
                    ->date()
                    ->sortable()
                    ->color('gray'),
            ])
            ->defaultSort('total_debit_balance', 'desc')
            ->striped();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageDebitNotes::route('/'),
        ];
    }
}
