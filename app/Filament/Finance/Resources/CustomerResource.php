<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\CustomerResource\Pages;
use App\Filament\Finance\Resources\CustomerResource\RelationManagers;
use App\Filament\Finance\Resources\CustomerResource\Widgets;
use App\Models\Customer;
use Filament\Forms\Form;
use Filament\Infolists\Infolist;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationGroup = 'Contacts';

    protected static ?int $navigationSort = -10;

    protected static ?string $recordTitleAttribute = 'name';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getRouteBaseName(?string $panel = null): string
    {
        return parent::getRouteBaseName('finance');
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'owner_name', 'email', 'phone'];
    }

    public static function getGlobalSearchResultUrl(Model $record): ?string
    {
        return static::getUrl('view', ['record' => $record]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return \App\Filament\Resources\CustomerResource::infolist($infolist);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->withMax('groups', 'arrival_date')
                ->withSum([
                    'invoices as total_invoice' => function ($query) {
                        $query->where('status', '!=', 'cancelled');
                    },
                ], DB::raw('total * exchange_rate'))
                ->withSum([
                    'invoices as total_payment' => function ($query) {
                        $query->where('status', '!=', 'cancelled');
                    },
                ], DB::raw('paid * exchange_rate'))
                ->withSum([
                    'invoices as receivable' => function ($query) {
                        $query->where('status', '!=', 'cancelled');
                    },
                ], DB::raw('(total - paid) * exchange_rate'))
            )
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID'),
                Tables\Columns\ImageColumn::make('logo')
                    ->label('')
                    ->getStateUsing(fn ($record) => $record->logoSquareUrl ?? $record->logoUrl),
                Tables\Columns\TextColumn::make('name')
                    ->label('Customer')
                    ->sortable()
                    ->description(fn ($record) => $record->email . ' · ' . $record->phone)
                    ->searchable(['customers.name', 'customers.email', 'phone']),
                Tables\Columns\TextColumn::make('total_invoice')
                    ->sortable()
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('total_payment')
                    ->sortable()
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('receivable')
                    ->sortable()
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('groups_max_arrival_date')
                    ->label('Latest arrival')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('agent')
                    ->relationship('agent', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->label('Details')
                        ->icon('heroicon-o-bars-3-bottom-left'),
                    Tables\Actions\Action::make('invoices')
                        ->url(fn ($record) => static::getUrl('invoices', ['record' => $record]))
                        ->icon('heroicon-o-document-duplicate'),
                    Tables\Actions\Action::make('statement')
                        ->url(fn ($record) => route('customers.statements', ['customerUuid' => $record->uuid]))
                        ->icon('heroicon-o-document-text')
                        ->openUrlInNewTab(),
                ]),
            ])
            ->defaultSort('groups_max_arrival_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCustomers::route('/'),
            'view' => Pages\ViewCustomer::route('/{record}'),
            'invoices' => Pages\Invoices::route('/{record}/invoices'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewCustomer::class,
            Pages\Invoices::class,
        ]);
    }

    public static function getRelations(): array
    {
        return [
            // RelationManagers\GroupCashesRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            // Widgets\CustomerOverview::class,
            Widgets\CustomerInvoiceOverview::class,
        ];
    }
}
