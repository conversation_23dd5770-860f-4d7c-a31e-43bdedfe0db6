<?php

namespace App\Filament\Finance\Resources;

use App\Enums\Finance\AccountCategory;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\BillPaymentResource\Pages;
use App\Models\Bill;
use App\Models\BillPayment;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\DebitNote;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BillPaymentResource extends Resource
{
    protected static ?string $model = BillPayment::class;

    protected static ?string $modelLabel = 'purchase payment';

    protected static ?string $navigationLabel = 'Payments';

    protected static ?string $navigationGroup = 'Purchases';

    protected static ?int $navigationSort = 20;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Details')
                            ->columns()
                            ->schema([
                                Forms\Components\Hidden::make('vendor_id'),
                                Forms\Components\Select::make('bill_id')
                                    ->label('Purchase')
                                    ->relationship('bill', 'bill_number', fn ($query) => $query->orderByDesc('bill_date'))
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $bill = Bill::find($state);
                                        $set('vendor_id', $bill?->vendor_id);
                                        $set('currency_code', $bill?->currency_code ?? 'SAR');
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($bill?->currency_code ?? 'SAR'));
                                    }),
                                Forms\Components\Select::make('user_id')
                                    ->label('Operator')
                                    ->relationship('user', 'name', fn ($query) => $query->role(['Admin', 'Operator', 'Finance']))
                                    ->searchable()
                                    ->preload()
                                    ->default(fn () => auth('web')->id())
                                    ->required(),
                                Forms\Components\Select::make('cash_account_id')
                                    ->label('From account')
                                    ->options(fn () => CashAccount::query()
                                        ->whereIn('category', [AccountCategory::CashBank, AccountCategory::AccountsReceivable, AccountCategory::Inventory, AccountCategory::CurrentAsset])
                                        ->orderBy('code')
                                        ->get()
                                        ->mapWithKeys(function ($account) {
                                            return [$account->id => $account->fullname];
                                        })
                                    )
                                    ->required()
                                    ->searchable()
                                    ->live(),
                                Forms\Components\Select::make('debit_note_id')
                                    ->label('Debit note')
                                    ->options(fn ($get) => DebitNote::query()
                                        ->with('bill')
                                        ->where('vendor_id', $get('vendor_id'))
                                        ->whereNull('used_for_bill_id')
                                        ->orderBy('bill_date')
                                        ->get()
                                        ->mapWithKeys(function ($debitNote) {
                                            return [$debitNote->id => $debitNote->bill->bill_number . ' (' . money($debitNote->amount, $debitNote->currency_code, true) . ')'];
                                        })
                                    )
                                    ->required()
                                    ->searchable()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $debitNote = DebitNote::find($state);
                                        if ($debitNote) {
                                            $set('currency_code', $debitNote->currency_code);
                                            $set('amount', $debitNote->amount);
                                            $set('exchange_rate', $debitNote->exchange_rate);
                                        }
                                    })
                                    ->visible(fn ($get) => $get('cash_account_id') == CashAccount::getIdForCode(config('finance.coa.debit_note'))),
                                Forms\Components\DateTimePicker::make('paid_at')
                                    ->label('Date')
                                    ->default(now())
                                    ->maxDate(today()->addDay())
                                    ->required(),
                                Forms\Components\TextInput::make('description')
                                    ->autocomplete('off')
                                    ->maxLength(255),

                                Forms\Components\ToggleButtons::make('currency_code')
                                    ->label('Currency')
                                    ->default('SAR')
                                    ->options(Currency::getOptions())
                                    ->inline()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    })
                                    ->disabled(fn ($get) => $get('cash_account_id') == CashAccount::getIdForCode(config('finance.coa.debit_note')))
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR')
                                    ->disabled(fn ($get) => $get('cash_account_id') == CashAccount::getIdForCode(config('finance.coa.debit_note')))
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->default(1)
                                    ->live(onBlur: true)
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->afterStateUpdated(function ($set, $state) {
                                        $set('exchange_rate', evaluate_math_string($state));
                                    })
                                    ->disabled(fn ($get) => $get('currency_code') === 'SAR')
                                    ->dehydrated(),

                                Forms\Components\FileUpload::make('attachment')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('purchase/payments')
                                    ->visibility('public'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Memo')
                            ->schema([
                                Forms\Components\Textarea::make('memo')
                                    ->hiddenLabel(),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query
                ->with(['bill.vendor', 'user', 'cash_account']))
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No.')
                    ->searchable(),
                Tables\Columns\TextColumn::make('bill.vendor.company_name')
                    ->label('Vendor')
                    ->toggleable()
                    ->toggledHiddenByDefault(),
                Tables\Columns\TextColumn::make('bill.bill_number')
                    ->label('Purchase')
                    ->searchable()
                    ->url(fn ($record) => BillResource::getUrl('view', ['record' => $record->bill_id])),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Operator')
                    ->searchable(),
                Tables\Columns\TextColumn::make('cash_account.fullname')
                    ->label('From account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto()
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('bill.vendor_id')
                    ->label('Vendor')
                    ->relationship('bill.vendor', 'company_name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Operator')
                    ->relationship('user', 'name', fn ($query) => $query->role(['Admin', 'Operator', 'Finance']))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('cash_account_id')
                    ->label('From account')
                    ->options(fn () => CashAccount::query()
                        ->whereIn('category', [AccountCategory::CashBank, AccountCategory::AccountsReceivable, AccountCategory::Inventory, AccountCategory::CurrentAsset])
                        ->orderBy('code')
                        ->get()
                        ->mapWithKeys(function ($account) {
                            return [$account->id => $account->fullname];
                        })
                    )
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->visible(fn ($record) => ! $record->user_cash_id)
                        ->mutateRecordDataUsing(fn ($record, $data) => [
                            ...$data,
                            'vendor_id' => $record->bill->vendor_id,
                        ]),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn ($record) => ! $record->user_cash_id),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->defaultSort('paid_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageBillPayments::route('/'),
        ];
    }
}
