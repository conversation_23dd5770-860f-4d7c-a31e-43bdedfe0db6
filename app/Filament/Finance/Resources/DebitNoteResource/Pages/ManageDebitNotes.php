<?php

namespace App\Filament\Finance\Resources\DebitNoteResource\Pages;

use App\Filament\Finance\Resources\DebitNoteResource;
use Filament\Resources\Pages\ManageRecords;

class ManageDebitNotes extends ManageRecords
{
    protected static string $resource = DebitNoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action needed since this is read-only
        ];
    }

    public function getTitle(): string
    {
        return 'Debit Notes';
    }
}
