<?php

namespace App\Filament\Finance\Resources\BillResource\Widgets;

use App\Filament\Finance\Resources\BillResource\Pages\ListBills;
use App\Models\Currency;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class BillsOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static bool $isLazy = false;

    protected function getTablePage(): string
    {
        return ListBills::class;
    }

    protected function getStats(): array
    {
        $data = $this->getData();
        $rate = Currency::getExchangeRate('IDR');

        return [
            Stat::make('Purchases', money($data->total_bills ?? 0, 'SAR', true))
                ->description(money($data->total_bills * $rate, 'IDR', true)),
            Stat::make('Payments', money($data->total_payments ?? 0, 'SAR', true))
                ->description(money($data->total_payments * $rate, 'IDR', true)),
            Stat::make('Payable', money($data->balance ?? 0, 'SAR', true))
                ->description(money($data->balance * $rate, 'IDR', true)),
        ];

        return [];
    }

    protected function getData()
    {
        $query = $this->getTablePageInstance()->getFilteredTableQuery()
            ->without(['vendor', 'order', 'groups']);
        $query->joins = null;

        return $query
            ->select([
                DB::raw('SUM(`total` * `exchange_rate`) AS total_bills'),
                DB::raw('SUM(`paid` * `exchange_rate`) AS total_payments'),
                DB::raw('SUM((`total` * `exchange_rate`) - (`paid` * `exchange_rate`)) AS balance'),
            ])
            ->first();
    }
}
