<?php

namespace App\Filament\Finance\Resources\BillResource\Pages;

use App\Enums\Finance\AccountCategory;
use App\Filament\Finance\Resources\BillResource;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\DebitNote;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;

class Payments extends ManageRelatedRecords
{
    protected static string $resource = BillResource::class;

    protected static string $relationship = 'payments';

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Payments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Details')
                            ->columns()
                            ->schema([
                                Forms\Components\Select::make('user_id')
                                    ->label('Operator')
                                    ->relationship('user', 'name', fn ($query) => $query->role(['Admin', 'Operator', 'Finance']))
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Forms\Components\Select::make('cash_account_id')
                                    ->label('From account')
                                    ->options(fn () => CashAccount::query()
                                        ->whereIn('category', [AccountCategory::CashBank, AccountCategory::AccountsReceivable, AccountCategory::Inventory, AccountCategory::CurrentAsset])
                                        ->orderBy('code')
                                        ->get()
                                        ->mapWithKeys(function ($account) {
                                            return [$account->id => $account->fullname];
                                        })
                                    )
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('debit_note_id')
                                    ->label('Debit note')
                                    ->options(fn () => DebitNote::query()
                                        ->with('bill')
                                        ->where('vendor_id', $this->getOwnerRecord()->vendor_id)
                                        ->whereNull('used_for_bill_id')
                                        ->orderBy('bill_date')
                                        ->get()
                                        ->mapWithKeys(function ($debitNote) {
                                            return [$debitNote->id => $debitNote->bill->bill_number . ' (' . money($debitNote->amount, $debitNote->currency_code, true) . ')'];
                                        })
                                    )
                                    ->required()
                                    ->searchable()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $debitNote = DebitNote::find($state);
                                        if ($debitNote) {
                                            $set('currency_code', $debitNote->currency_code);
                                            $set('amount', $debitNote->amount);
                                            $set('exchange_rate', $debitNote->exchange_rate);
                                        }
                                    })
                                    ->visible(fn ($get) => $get('cash_account_id') == CashAccount::getIdForCode(config('finance.coa.debit_note'))),
                                Forms\Components\DateTimePicker::make('paid_at')
                                    ->label('Date')
                                    ->required(),
                                Forms\Components\TextInput::make('description')
                                    ->autocomplete('off')
                                    ->maxLength(255),

                                Forms\Components\ToggleButtons::make('currency_code')
                                    ->label('Currency')
                                    ->default('SAR')
                                    ->options(Currency::getOptions())
                                    ->inline()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    })
                                    ->disabled(fn ($get) => $get('cash_account_id') == CashAccount::getIdForCode(config('finance.coa.debit_note')))
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR')
                                    ->disabled(fn ($get) => $get('cash_account_id') == CashAccount::getIdForCode(config('finance.coa.debit_note')))
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->default(1)
                                    ->live(onBlur: true)
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->afterStateUpdated(function ($set, $state) {
                                        $set('exchange_rate', evaluate_math_string($state));
                                    })
                                    ->disabled(fn ($get) => $get('currency_code') === 'SAR')
                                    ->dehydrated(),

                                Forms\Components\FileUpload::make('attachment')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('purchase/payments')
                                    ->visibility('public'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Memo')
                            ->schema([
                                Forms\Components\Textarea::make('memo')
                                    ->hiddenLabel(),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('payment')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No.'),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Operator'),
                Tables\Columns\TextColumn::make('cash_account.fullname')
                    ->label('From account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mountUsing(fn ($form) => $form->fill([
                        'paid_at' => now(),
                        'currency_code' => $this->getOwnerRecord()->currency_code,
                        'exchange_rate' => 1 / Currency::getExchangeRate($this->getOwnerRecord()->currency_code),
                    ])),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => ! $record->user_cash_id),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => ! $record->user_cash_id),
            ])
            ->defaultSort('paid_at', 'desc');
    }
}
