<?php

namespace App\Filament\Pages\Widgets;

use App\Enums\VendorType;
use App\Models\Vendor;
use Filament\Support\Colors\Color;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class TopMuassasahsChart extends ChartWidget
{
    protected static ?string $heading = 'Top Muassasahs';

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = null;

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array | RawJs | null
    {
        return [
            'indexAxis' => 'y',
        ];
    }

    protected function getData(): array
    {
        $period = current_period();
        $muassasahs = Vendor::query()
            ->where('vendor_type', VendorType::Muassasah)
            ->leftJoin('groups', 'vendors.id', '=', 'groups.muassasah_id')
            ->whereBetween('groups.arrival_date', [
                $period->date_start->format('Y-m-d') . ' 00:00:00',
                $period->date_end->format('Y-m-d') . ' 23:59:59',
            ])
            ->select([
                'vendors.id',
                'vendors.company_name',
                DB::raw('count(`groups`.`id`) AS groups_count'),
                DB::raw('sum(`groups`.`total_pax`) AS pax_count'),
            ])
            ->groupBy(['vendors.id', 'vendors.company_name'])
            ->orderByDesc('pax_count')
            ->limit(10)
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Groups',
                    'data' => $muassasahs->pluck('groups_count'),
                    'backgroundColor' => 'rgb(' . Color::Blue[500] . ')',
                    'borderColor' => 'transparent',
                ],
                [
                    'label' => 'Total Pax',
                    'data' => $muassasahs->pluck('pax_count'),
                    'backgroundColor' => 'rgb(' . Color::Amber[500] . ')',
                    'borderColor' => 'transparent',
                ],
            ],
            'labels' => $muassasahs->pluck('company_name'),
        ];
    }
}
