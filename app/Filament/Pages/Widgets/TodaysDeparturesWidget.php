<?php

namespace App\Filament\Pages\Widgets;

use App\Filament\Resources\GroupResource;
use App\Models\GroupFlight;
use App\Models\User;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Closure;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class TodaysDeparturesWidget extends BaseWidget
{
    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $heading = 'Departures';

    public $date;

    public static function canView(): bool
    {
        return auth()->user()->can('groups.view') || auth()->user()->hasRole(['Admin', 'Operator', 'Airport Handler', 'Mutawif']);
    }

    protected function getTableQuery(): Builder
    {
        /** @var User */
        $user = auth()->user();

        return GroupFlight::query()
            ->with([
                'airline',
                'group.customer',
                'group.muassasah',
            ])
            ->where('type', 'departure')
            ->when($user->hasExactRoles('Airport Handler'), function ($query) use ($user) {
                $userVendorIds = $user->vendors()->pluck('id');
                $airportCodes = DB::table('airport_handler')
                    ->whereIn('handler_id', $userVendorIds)
                    ->pluck('airport_code');

                if (filled($airportCodes)) {
                    $query->whereIn('from', $airportCodes);
                }

                if (count($userVendorIds) > 0) {
                    $query->whereIn('handler_id', $userVendorIds);
                }
            })
            ->when($user->hasExactRoles('Mutawif'), function ($query) use ($user) {
                $query->whereHas('group', function ($query) use ($user) {
                    $query->where('mutawif_id', $user->id)
                        ->orWhere('mutawif_2_id', $user->id)
                        ->orWhere('mutawif_3_id', $user->id);
                });
            })
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->whereDate('date_etd', '=', $this->date);
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No departures';
    }

    protected function getTableRecordUrlUsing(): ?Closure
    {
        return function (GroupFlight $groupFlight) {
            return GroupResource::getUrl('view', ['record' => $groupFlight->group_id]);
        };
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('date_etd')
                ->date()
                ->sortable()
                ->label('Date'),
            Tables\Columns\TextColumn::make('from')
                ->label('Location'),
            BadgeableColumn::make('flight_number')
                ->label('Flight')
                ->description(fn ($record) => $record->airline?->name ?? null)
                ->suffixBadges([
                    Badge::make('pax')
                        ->label(fn ($record) => $record->total_pax . ' pax'),
                ]),
            Tables\Columns\TextColumn::make('etd')
                ->label('ETD'),
            Tables\Columns\TextColumn::make('eta')
                ->label('ETA'),
            Tables\Columns\TextColumn::make('group')
                ->getStateUsing(fn ($record) => $record->group->customer?->name ?? '-')
                ->description(fn ($record) => $record->group->name)
                ->label('Group'),
            Tables\Columns\TextColumn::make('muassasah')
                ->label('Muassasah')
                ->getStateUsing(fn ($record) => $record->group->muassasah?->company_name)
                ->description(fn ($record) => $record->group->number)
                ->toggleable(),
            Tables\Columns\TextColumn::make('services')
                ->label('Services')
                ->badge()
                ->getStateUsing(fn ($record) => $record->group->getServices())
                ->color('primary')
                ->toggleable(),
        ];
    }
}
