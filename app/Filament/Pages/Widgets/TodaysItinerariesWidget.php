<?php

namespace App\Filament\Pages\Widgets;

use App\Filament\Resources\GroupResource;
use App\Filament\Tables\Columns\WhatsAppColumn;
use App\Models\Itinerary;
use Filament\Forms;
use Filament\Support\Colors\Color;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Umrahservice\Groups\Enums\GroupStatus;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class TodaysItinerariesWidget extends BaseWidget
{
    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $heading = 'Schedules';

    public $date;

    public static function canView(): bool
    {
        return auth()->user()->can('groups.view') || auth()->user()->hasRole(['Admin', 'Operator', 'Mutawif']);
    }

    protected function getTableQuery(): Builder
    {
        $user = auth()->user();

        return Itinerary::query()
            ->with(['group.vehicles', 'contacts'])
            ->leftJoin('groups', 'groups.id', '=', 'itineraries.group_id')
            ->leftJoin('customers', 'customers.id', '=', 'groups.customer_id')
            ->leftJoin('vendors as muassasahs', 'muassasahs.id', '=', 'groups.muassasah_id')
            ->when($user->hasExactRoles('Mutawif'), function ($query) use ($user) {
                $query->where(function ($q) use ($user) {
                    $q->where('groups.mutawif_id', $user->id)
                        ->orWhere('groups.mutawif_2_id', $user->id)
                        ->orWhere('groups.mutawif_3_id', $user->id);
                });
            })
            ->whereDate('date', $this->date)
            ->where('groups.status', GroupStatus::Confirmed)
            ->select([
                'itineraries.id',
                'location',
                'description',
                'muassasahs.company_name AS muassasah',
                'customers.name As customer',
                'groups.id AS group_id',
                'groups.name As group_name',
                'groups.number As group_number',
                'date',
            ])
            ->orderBy('date');
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No schedules';
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('date')
                ->badge()
                ->label('Time')
                ->color('primary')
                ->time('H:i'),
            Tables\Columns\TextColumn::make('vehicles')
                ->getStateUsing(fn ($record) => $record->group->vehicles?->map(fn ($v) => $v->pivot->count . ' x ' . $v->name))
                ->badge()
                ->color(Color::Green),
            Tables\Columns\TextColumn::make('details')
                ->getStateUsing(fn ($record) => $record->location)
                ->description(fn ($record) => $record->description),
            Tables\Columns\TextColumn::make('muassasah')
                ->label('Muassasah')
                ->description(fn ($record) => $record->group_number),
            Tables\Columns\TextColumn::make('group')
                ->getStateUsing(fn ($record) => $record->customer)
                ->description(fn ($record) => $record->group_name)
                ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group_id])),
            Tables\Columns\TextColumn::make('services')
                ->label('Services')
                ->badge()
                ->getStateUsing(fn ($record) => $record->group->getServices())
                ->color('primary')
                ->toggleable(),
            WhatsAppColumn::make('contact')
                ->contactName(fn ($record) => $record->contacts[0]['name'] ?? null)
                ->contactPhone(fn ($record) => $record->contacts[0]['phone'] ?? null),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Tables\Actions\EditAction::make('set_contact')
                ->label('Set contact')
                ->color('gray')
                ->form([
                    Forms\Components\Select::make('contacts')
                        ->relationship('contacts', 'name')
                        ->multiple()
                        ->preload()
                        ->searchable()
                        ->createOptionForm([
                            Forms\Components\TextInput::make('name')
                                ->required(),
                            PhoneInput::make('phone')
                                ->required(),
                            Forms\Components\TextInput::make('email')
                                ->email(),
                        ])
                        ->createOptionAction(fn ($action) => $action->modalWidth('sm')->extraModalFooterActions([])),
                ])
                ->modalWidth('sm'),
        ];
    }
}
