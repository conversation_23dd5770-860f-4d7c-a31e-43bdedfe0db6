<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class Statistics extends Page
{
    protected static ?string $navigationGroup = 'Statistics';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?int $navigationSort = 0;

    protected static string $view = 'filament-panels::pages.dashboard';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator', 'Finance']);
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getWidgets(): array
    {
        return [
            Widgets\MonthlyPax::class,
            Widgets\TopServicesTable::class,
            Widgets\TopHotelsChart::class,
            Widgets\TopCustomersChart::class,
            Widgets\TopMuassasahsChart::class,
        ];
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getVisibleWidgets(): array
    {
        return $this->filterVisibleWidgets($this->getWidgets());
    }

    /**
     * @return int | string | array<string, int | string | null>
     */
    public function getColumns(): int | string | array
    {
        return 2;
    }
}
