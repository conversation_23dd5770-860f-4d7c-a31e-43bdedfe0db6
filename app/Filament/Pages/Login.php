<?php

namespace App\Filament\Pages;

use <PERSON><PERSON><PERSON><PERSON>\FilamentOtpLogin\Filament\Pages\Login as BaseLogin;
use <PERSON><PERSON><PERSON>r\FilamentOtpLogin\Notifications\SendOtpCode;
use App\Models\User;
use Filament\Notifications\Notification;

class Login extends BaseLogin
{
    protected function sendOtpToUser(string $otpCode): void
    {
        $this->email = $this->data['email'];
        $user = User::where('email', $this->email)->first();

        $notificationClass = config('filament-otp-login.notification_class', SendOtpCode::class);

        $user->notify(new $notificationClass($otpCode));

        Notification::make()
            ->title(__('filament-otp-login::translations.notifications.title'))
            ->body(__('filament-otp-login::translations.notifications.body', ['seconds' => config('filament-otp-login.otp_code.expires')]))
            ->success()
            ->send();
    }
}
