<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Filament\Resources\GroupResource;
use App\Models\Room;
use Filament\Infolists;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;

class PilgrimDetails extends Page
{
    use InteractsWithRecord;

    protected static string $resource = GroupResource::class;

    protected static string $view = 'filament.resources.group-resource.pages.pilgrim-details';

    protected static string $layout = 'filament-panels::components.layout.simple';

    public Model | int | string | null $pilgrim;

    public Model | int | string | null $room;

    public function mount(int | string $record, int | string $pilgrim): void
    {
        $this->record = $this->resolveRecord($record);
        $this->pilgrim = $this->record->pilgrims()->find($pilgrim);
        $this->room = Room::find($this->pilgrim->pivot->room_id);

        $this->authorizeAccess();
    }

    protected function authorizeAccess(): void
    {
        abort_unless(static::getResource()::canView($this->getRecord()), 403);
    }

    public function getHeading(): string | Htmlable
    {
        return '';
    }

    public function hasLogo(): bool
    {
        return false;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $handlers = collect($this->record->hotel_handlers)->keyBy('city');

        $hotels = $this->record->hotels;
        $hotels_makkah = $hotels->filter(fn ($hotel) => $hotel->city === 'Makkah');
        $hotels_madinah = $hotels->filter(fn ($hotel) => $hotel->city === 'Madinah');
        $room_numbers = collect($this->room->meta['room_numbers'] ?? []);
        $hotel_makkah_id = $room_numbers->whereIn('hotel_id', $hotels_makkah->pluck('id'))->where('room_number', '!=', '')->first()['hotel_id']
            ?? $hotels_makkah->first()?->id;
        $hotel_madinah_id = $room_numbers->whereIn('hotel_id', $hotels_madinah->pluck('id'))->where('room_number', '!=', '')->first()['hotel_id']
            ?? $hotels_madinah->first()?->id;

        return $infolist
            ->state([
                'group' => $this->record,
                'pilgrim' => $this->pilgrim,
                'room' => $this->room,
                'hotel_makkah' => $this->record->hotels()->where('hotels.id', $hotel_makkah_id)->pluck('name'),
                'hotel_madinah' => $this->record->hotels()->where('hotels.id', $hotel_madinah_id)->pluck('name'),
            ])
            ->inlineLabel()
            ->schema([
                Infolists\Components\ViewEntry::make('lugage_tag')
                    ->hiddenLabel()
                    ->view('components.pilgrim-luggage-tag', [
                        'pilgrim' => $this->pilgrim,
                        'group' => $this->record,
                    ]),
                ImageEntry::make('pilgrim.photo_url')
                    ->circular()
                    ->hiddenLabel()
                    ->label('Photo'),

                TextEntry::make('pilgrim.fullname')
                    ->label('Full Name'),

                TextEntry::make('pilgrim.passport_number')
                    ->label('Passport Number'),

                ...collect([
                    $this->record->tour_leader ? TextEntry::make('group.tour_leader.name')
                        ->label('Tour Leader')
                        ->helperText($this->record->tour_leader->phone)
                        ->suffixAction(
                            InfoLists\Components\Actions\Action::make('chat')
                                ->icon('tabler-brand-whatsapp')
                                ->color('success')
                                ->url(wa_chat_url($this->record->tour_leader->phone))
                                ->openUrlInNewTab()
                                ->visible((bool) $this->record->tour_leader->phone)
                        ) : null,
                ])->filter()->toArray(),

                ...collect([1, 2, 3])->map(function ($index) {
                    return Infolists\Components\TextEntry::make('group.mutawif' . ($index > 1 ? "_{$index}" : ''))
                        ->label('Mutawif' . ($index > 1 ? " {$index}" : ''))
                        ->formatStateUsing(fn ($state) => $state?->name)
                        ->helperText(fn ($state) => $state?->phone)
                        ->visible(fn ($state) => (bool) $state)
                        ->suffixAction(
                            fn ($state) => $state
                            ? InfoLists\Components\Actions\Action::make('chat')
                                ->icon('tabler-brand-whatsapp')
                                ->color('success')
                                ->url(wa_chat_url($state->phone))
                                ->openUrlInNewTab()
                                ->visible((bool) $state->phone)
                                : null
                        );
                })->toArray(),

                TextEntry::make('hotel_makkah')
                    ->label('Hotel Makkah')
                    ->listWithLineBreaks(),

                TextEntry::make('hotel_madinah')
                    ->label('Hotel Madinah')
                    ->listWithLineBreaks(),

                Infolists\Components\TextEntry::make('makkah_handlers')
                    ->label('Tim Makkah')
                    ->state($handlers->get('Makkah')['name'] ?? null)
                    ->helperText(fn ($state) => $handlers->get('Makkah')['phone'] ?? null)
                    ->suffixAction(
                        InfoLists\Components\Actions\Action::make('chat')
                            ->icon('tabler-brand-whatsapp')
                            ->color('success')
                            ->url(wa_chat_url($handlers->get('Makkah')['phone'] ?? null))
                            ->openUrlInNewTab()
                            ->visible((bool) ($handlers->get('Makkah')['phone'] ?? false))
                    ),

                Infolists\Components\TextEntry::make('madinah_handlers')
                    ->label('Tim Madinah')
                    ->state($handlers->get('Madinah')['name'] ?? null)
                    ->helperText(fn ($state) => $handlers->get('Madinah')['phone'] ?? null)
                    ->suffixAction(
                        InfoLists\Components\Actions\Action::make('chat')
                            ->icon('tabler-brand-whatsapp')
                            ->color('success')
                            ->url(wa_chat_url($handlers->get('Madinah')['phone'] ?? null))
                            ->openUrlInNewTab()
                            ->visible((bool) ($handlers->get('Madinah')['phone'] ?? false))
                    ),

                TextEntry::make('group.muassasah.company_name')
                    ->label('Muassasah'),
            ]);
    }
}
