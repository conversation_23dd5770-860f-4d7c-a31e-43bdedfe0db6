<?php

namespace App\Filament\Resources\VendorResource\Pages;

use App\Enums\VendorType;
use App\Filament\Resources\VendorResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;

class ManageVendors extends ManageRecords
{
    protected static string $resource = VendorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return collect(VendorType::cases())
            ->mapWithKeys(function ($type) {
                $tabKey = strtolower($type->name);

                return [
                    $tabKey => Tab::make($type->getLabel())->modifyQueryUsing(
                        fn (Builder $query) => $query->where('vendor_type', $type)
                    ),
                ];
            })
            ->toArray();
    }
}
