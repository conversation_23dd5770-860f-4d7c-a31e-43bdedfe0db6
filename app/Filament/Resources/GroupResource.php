<?php

namespace App\Filament\Resources;

use App\Enums\ContactType;
use App\Filament\Finance\Resources\GroupFinanceResource;
use App\Filament\Resources\GroupResource\Pages;
use App\Filament\Resources\GroupResource\RelationManagers;
use App\Filament\Resources\GroupResource\Widgets;
use App\Models\Finance\Invoice;
use App\Models\Group;
use App\Models\Hotel;
use App\Models\User;
use App\Models\Vehicle;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Umrahservice\Groups\Enums\GroupProgress;
use Umrahservice\Groups\Enums\GroupStatus;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class GroupResource extends Resource
{
    protected static ?string $model = Group::class;

    protected static ?string $navigationGroup = 'Groups';

    protected static ?int $navigationSort = -2;

    protected static ?string $navigationLabel = 'PIF Groups';

    protected static ?string $recordTitleAttribute = 'name';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getRouteBaseName(?string $panel = null): string
    {
        return parent::getRouteBaseName('admin');
    }

    public static function getEloquentQuery(): Builder
    {
        /** @var User */
        $user = auth('web')->user();

        return parent::getEloquentQuery()
            ->with(['customer', 'invoices', 'created_by', 'updated_by'])
            ->when($user->hasExactRoles('Airport Handler'), function ($query) use ($user) {
                $query
                    ->whereHas('flights', function ($query) use ($user) {
                        $userVendorIds = $user->vendors()->pluck('id');
                        $query->whereIn('handler_id', $userVendorIds);
                    });
            })
            ->when($user->hasExactRoles('Mutawif'), function ($query) use ($user) {
                $query->where(function ($q) use ($user) {
                    $q->where('mutawif_id', $user->id)
                        ->orWhere('mutawif_2_id', $user->id)
                        ->orWhere('mutawif_3_id', $user->id);
                });
            })
            ->when($user->hasExactRoles('Customer'), function ($query) use ($user) {
                $query->where(function ($query) use ($user) {
                    $query->whereHas('customer', fn ($query) => $query->where('user_id', $user->id));
                });
            });
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['customer.name', 'name', 'number'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        /** @var Group $record */
        return [
            'Customer' => $record->customer->name,
            'رقم' => $record->number,
        ];
    }

    public static function getGlobalSearchResultUrl(Model $record): ?string
    {
        return static::getUrl('view', ['record' => $record]);
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()
            ->with(['customer'])
            ->currentPeriod();
    }

    public static function getGlobalSearchResultActions(Model $record): array
    {
        return [
            \Filament\GlobalSearch\Actions\Action::make('manifest')
                ->url(static::getUrl('manifest', ['record' => $record])),
            \Filament\GlobalSearch\Actions\Action::make('closing report')
                ->url(GroupFinanceResource::getUrl('closing-report', ['record' => $record])),
        ];
    }

    public static function getRecordTitle(?Model $record): ?string
    {
        /** @var Group|null $record */
        return $record?->full_name ?? 'Untitled';
    }

    public static function form(Form $form): Form
    {
        $hotels = Hotel::query()
            ->orderBy('city')
            ->orderBy('name')
            ->get()
            ->mapWithKeys(fn ($h) => [$h->id => "{$h->city} - {$h->name}"])
            ->toArray();
        $vehicles = Vehicle::query()->get()
            ->mapWithKeys(fn ($h) => [$h->id => "{$h->name} ({$h->capacity} pax)"])
            ->toArray();

        return $form
            ->columns(1)
            ->schema([
                Forms\Components\View::make('components.floating-save-bar'),
                Forms\Components\Tabs::make('Group Details')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Basic Information')
                            ->columns(2)
                            ->schema([
                                // Basic information section
                                Forms\Components\Grid::make(5)
                                    ->columnSpanFull()
                                    ->schema([
                                        Forms\Components\Select::make('customer_id')
                                            ->relationship('customer', 'name')
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->default(fn () => request('customer_id'))
                                            ->columnSpan(['lg' => 3]),
                                        Forms\Components\TextInput::make('name')
                                            ->label('Group Name')
                                            ->columnSpan(['lg' => 2]),
                                        Forms\Components\Group::make()
                                            ->columnSpan(['lg' => 2])
                                            ->schema([
                                                Forms\Components\ToggleButtons::make('services')
                                                    ->label('Services')
                                                    ->multiple()
                                                    ->options(Group::GROUP_SERVICES)
                                                    ->default(['handling', 'visa', 'hotel'])
                                                    ->inline()
                                                    ->required()
                                                    ->live(),
                                                Forms\Components\Toggle::make('meta.use_radiophone')
                                                    ->label('Radiophone')
                                                    ->default(false),
                                            ]),
                                        Forms\Components\DatePicker::make('arrival_date')
                                            ->label('Arrival Date')
                                            ->required(),
                                        Forms\Components\DatePicker::make('departure_date')
                                            ->label('Departure Date'),
                                        Forms\Components\TextInput::make('total_pax')
                                            ->label('Total Pax')
                                            ->numeric()
                                            ->required()
                                            ->columnSpan(['lg' => 1]),
                                    ]),
                                // Muassasah section
                                Forms\Components\Section::make('Muassasah')
                                    ->compact()
                                    ->columnSpan(['lg' => 1])
                                    ->schema([
                                        Forms\Components\Select::make('muassasah_id')
                                            ->label('Muassasah')
                                            ->relationship('muassasah', 'company_name')
                                            ->searchable()
                                            ->preload(),
                                        Forms\Components\TextInput::make('number')
                                            ->label('رقم المجموعة'),
                                    ]),
                                // Invoices section
                                Forms\Components\Section::make('Invoices')
                                    ->compact()
                                    ->columnSpan(['lg' => 1])
                                    ->schema([
                                        Forms\Components\Select::make('invoice_ids')
                                            ->label('Invoices')
                                            ->multiple()
                                            ->searchable()
                                            ->options(fn ($record, $get) => Invoice::query()
                                                ->where('customer_id', $get('customer_id'))
                                                ->where(function ($query) use ($record) {
                                                    $query
                                                        ->when($record, fn ($query) => $query->where('group_id', $record->id)->orWhereNull('group_id'))
                                                        ->when(! $record, fn ($query) => $query->whereNull('group_id'));
                                                })
                                                ->pluck('invoice_number', 'id')
                                            ),
                                    ])
                                    ->visible(fn ($context) => $context !== 'create'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Personnel')
                            ->schema([
                                // Tour Leader & Mutawif section
                                Forms\Components\Section::make('Tour Leader & Mutawif')
                                    ->columns(['lg' => 2])
                                    ->compact()
                                    ->schema([
                                        Forms\Components\Group::make()
                                            ->schema([
                                                Forms\Components\Select::make('tour_leader_id')
                                                    ->label('Tour Leader')
                                                    ->relationship('tour_leader', 'name')
                                                    ->searchable()
                                                    ->preload()
                                                    ->createOptionForm([
                                                        Forms\Components\Hidden::make('type')
                                                            ->default(ContactType::TourLeader),
                                                        Forms\Components\TextInput::make('name')
                                                            ->required(),
                                                        Forms\Components\TextInput::make('email')
                                                            ->email(),
                                                        PhoneInput::make('phone')
                                                            ->required(),
                                                    ])
                                                    ->createOptionAction(fn ($action) => $action->modalWidth('sm'))
                                                    ->editOptionForm([
                                                        Forms\Components\TextInput::make('name')
                                                            ->required(),
                                                        Forms\Components\TextInput::make('email')
                                                            ->email(),
                                                        PhoneInput::make('phone')
                                                            ->required(),
                                                    ])
                                                    ->editOptionAction(fn ($action) => $action->modalWidth('sm')),
                                            ])
                                            ->columnSpan(1)
                                            ->columns(1),
                                        Forms\Components\Group::make()
                                            ->schema([
                                                Forms\Components\Select::make('mutawif_id')
                                                    ->label('Mutawif')
                                                    ->relationship('mutawif', 'name_with_phone')
                                                    ->createOptionForm([
                                                        Forms\Components\TextInput::make('name')
                                                            ->required(),
                                                        Forms\Components\TextInput::make('email')
                                                            ->required()
                                                            ->unique(),
                                                        PhoneInput::make('phone')
                                                            ->required(),
                                                        Forms\Components\TextInput::make('password')
                                                            ->required()
                                                            ->password()
                                                            ->dehydrateStateUsing(fn ($state) => Hash::make($state)),
                                                    ])
                                                    ->createOptionAction(fn ($action) => $action->modalWidth('sm'))
                                                    ->createOptionUsing(function ($data) {
                                                        /** @var User */
                                                        $mutawif = User::create($data);

                                                        $mutawif->syncRoles(['Mutawif']);
                                                        $mutawif->email_verified_at = Carbon::now();
                                                        $mutawif->save();

                                                        return $mutawif->getKey();
                                                    })
                                                    ->editOptionForm([
                                                        Forms\Components\TextInput::make('name')
                                                            ->required(),
                                                        PhoneInput::make('phone')
                                                            ->required(),
                                                    ])
                                                    ->editOptionAction(fn ($action) => $action->modalWidth('sm'))
                                                    ->searchable()
                                                    ->preload(),
                                                Forms\Components\Section::make('Additional Mutawifs')
                                                    ->compact()
                                                    ->columns(1)
                                                    ->collapsible()
                                                    ->collapsed()
                                                    ->schema([
                                                        Forms\Components\Select::make('mutawif_2_id')
                                                            ->label('Mutawif 2')
                                                            ->relationship('mutawif_2', 'name_with_phone')
                                                            ->searchable()
                                                            ->preload(),
                                                        Forms\Components\Select::make('mutawif_3_id')
                                                            ->label('Mutawif 3')
                                                            ->relationship('mutawif_3', 'name_with_phone')
                                                            ->searchable()
                                                            ->preload(),
                                                    ]),
                                            ])
                                            ->columnSpan(1)
                                            ->columns(1),
                                    ]),
                                // Hotel Handlers section
                                Forms\Components\Section::make('Hotel Handlers')
                                    ->compact()
                                    ->schema([
                                        TableRepeater::make('hotel_handlers')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add Hotel Handler')
                                            ->defaultItems(0)
                                            ->headers([
                                                Header::make('City'),
                                                Header::make('Handler'),
                                            ])
                                            ->schema([
                                                Forms\Components\Select::make('city')
                                                    ->options(Hotel::getCities()),
                                                Forms\Components\Select::make('handler_id')
                                                    ->options(fn () => User::query()
                                                        ->role(['Operator'])
                                                        ->pluck('name', 'id'))
                                                    ->searchable(),
                                            ]),
                                    ]),
                            ]),
                        Forms\Components\Tabs\Tab::make('Accommodation & Transportation')
                            ->schema([
                                // Hotels section
                                Forms\Components\Section::make('Hotels')
                                    ->visible(fn ($context) => $context === 'create')
                                    ->compact()
                                    ->schema([
                                        TableRepeater::make('group_hotels')
                                            ->hiddenLabel()
                                            ->relationship('group_hotels')
                                            ->addActionLabel('Add Hotel')
                                            ->defaultItems(0)
                                            ->reorderable(false)
                                            ->headers([
                                                Header::make('Hotel'),
                                                Header::make('Check In'),
                                                Header::make('Check Out'),
                                                Header::make('Single'),
                                                Header::make('Double'),
                                                Header::make('Triple'),
                                                Header::make('Quad'),
                                                Header::make('Quint'),
                                            ])
                                            ->schema([
                                                Forms\Components\Select::make('hotel_id')
                                                    ->options($hotels)
                                                    ->searchable()
                                                    ->required(),
                                                Forms\Components\DatePicker::make('check_in')
                                                    ->required(),
                                                Forms\Components\DatePicker::make('check_out')
                                                    ->required(),
                                                Forms\Components\TextInput::make('room_single_count')
                                                    ->numeric()
                                                    ->extraAttributes([
                                                        'class' => 'w-16',
                                                    ])
                                                    ->required()
                                                    ->default(0),
                                                Forms\Components\TextInput::make('room_double_count')
                                                    ->numeric()
                                                    ->extraAttributes([
                                                        'class' => 'w-16',
                                                    ])
                                                    ->required()
                                                    ->default(0),
                                                Forms\Components\TextInput::make('room_triple_count')
                                                    ->numeric()
                                                    ->extraAttributes([
                                                        'class' => 'w-16',
                                                    ])
                                                    ->required()
                                                    ->default(0),
                                                Forms\Components\TextInput::make('room_quad_count')
                                                    ->numeric()
                                                    ->extraAttributes([
                                                        'class' => 'w-16',
                                                    ])
                                                    ->required()
                                                    ->default(0),
                                                Forms\Components\TextInput::make('room_quint_count')
                                                    ->numeric()
                                                    ->extraAttributes([
                                                        'class' => 'w-16',
                                                    ])
                                                    ->required()
                                                    ->default(0),
                                            ]),
                                    ]),
                                Forms\Components\TextInput::make('meta.room_service')
                                    ->label('Room Service')
                                    ->inlineLabel(),
                                // Transportation section
                                Forms\Components\Section::make('Transportation')
                                    ->compact()
                                    ->schema([
                                        Forms\Components\Select::make('transport_id')
                                            ->label('Company')
                                            ->relationship('transport', 'company_name')
                                            ->columnSpanFull(),
                                        TableRepeater::make('group_vehicles')
                                            ->relationship('group_vehicles')
                                            ->label('Vehicles')
                                            ->addActionLabel('Add Vehicle')
                                            ->columnSpanFull()
                                            ->defaultItems(0)
                                            ->headers([
                                                Header::make('Vehicle'),
                                                Header::make('Count'),
                                                Header::make('Total Pax'),
                                            ])
                                            ->schema([
                                                Forms\Components\Select::make('vehicle_id')
                                                    ->options($vehicles)
                                                    ->searchable()
                                                    ->required(),
                                                Forms\Components\TextInput::make('count')
                                                    ->numeric()
                                                    ->required()
                                                    ->default(1),
                                                Forms\Components\TextInput::make('pax_count')
                                                    ->numeric()
                                                    ->required()
                                                    ->default(0),
                                            ]),
                                        // Train Lines section
                                        Forms\Components\Repeater::make('meta.trains')
                                            ->label('Train Lines')
                                            ->visible(fn ($get) => in_array('train', $get('services') ?? []))
                                            ->columnSpanFull()
                                            ->columns(['md' => 3])
                                            ->addActionLabel('Add Train Line')
                                            ->cloneable()
                                            ->reorderableWithDragAndDrop()
                                            ->schema([
                                                Forms\Components\Select::make('line')
                                                    ->label('Line')
                                                    ->options(Group::getTrainLines())
                                                    ->required(),
                                                Forms\Components\DateTimePicker::make('etd')
                                                    ->label('ETD')
                                                    ->seconds(false)
                                                    ->required(),
                                                Forms\Components\DateTimePicker::make('eta')
                                                    ->label('ETA')
                                                    ->seconds(false)
                                                    ->required(),
                                            ]),
                                    ]),
                            ]),
                        Forms\Components\Tabs\Tab::make('Activities & Meals')
                            ->schema([
                                // Manasik section
                                Forms\Components\Section::make('Manasik')
                                    ->compact()
                                    ->schema([
                                        TableRepeater::make('manasiks')
                                            ->relationship('manasiks')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add Manasik')
                                            ->defaultItems(0)
                                            ->reorderable()
                                            ->headers([
                                                Header::make('Manasik'),
                                                Header::make('Date & Time'),
                                                Header::make('Additional Facilities'),
                                            ])
                                            ->schema([
                                                Forms\Components\TextInput::make('name')
                                                    ->required(),
                                                Forms\Components\DateTimePicker::make('date')
                                                    ->required(),
                                                Forms\Components\TextInput::make('meta.addons'),
                                            ])
                                            ->columns(3),
                                    ]),
                                // Meals section
                                Forms\Components\Section::make('Meals')
                                    ->compact()
                                    ->schema([
                                        Forms\Components\TextInput::make('meals.arrival')
                                            ->label('Arrival Meals'),
                                        Forms\Components\TextInput::make('meals.departure')
                                            ->label('Departure Meals'),
                                        Forms\Components\TextInput::make('meals.extra')
                                            ->label('Extra Meals'),
                                    ])
                                    ->columns(['md' => 3]),
                            ]),
                        Forms\Components\Tabs\Tab::make('Etc.')
                            ->schema([
                                Forms\Components\Section::make('Notes')
                                    ->compact()
                                    ->schema([
                                        Forms\Components\Repeater::make('meta.notes')
                                            ->hiddenLabel()
                                            ->simple(Forms\Components\TextInput::make('note')),
                                    ]),
                            ]),
                    ])
                    ->activeTab(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordClasses(fn ($record) => match ($record->status) {
                GroupStatus::Draft => 'bg-warning-500/10',
                GroupStatus::Cancelled => 'bg-danger-500/10',
                default => null,
            })
            ->groups([
                Tables\Grouping\Group::make('customer.name')
                    ->titlePrefixedWithLabel(false),
                Tables\Grouping\Group::make('arrival_date')
                    ->date(),
            ])
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID'),
                // Tables\Columns\TextColumn::make('invoice')
                //     ->label('Invoice')
                //     ->money('SAR')
                //     ->getStateUsing(fn ($record) => $record->invoice ? $record->invoice->total * $record->invoice->exchange_rate : null)
                //     ->description(fn ($record) => $record->invoice?->invoice_number ?? null, 'above')
                //     ->toggleable()
                //     ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
                BadgeableColumn::make('group')
                    ->label('Group')
                    ->getStateUsing(fn ($record) => $record->customer->name)
                    ->description(fn ($record) => $record->name)
                    ->searchable(['name', 'number'])
                    ->suffixBadges([
                        Badge::make('pax')
                            ->label(fn ($record) => $record->total_pax . ' pax'),
                        Badge::make('status')
                            ->color(fn ($record) => $record->status->getColor())
                            ->label(fn ($record) => $record->status->getLabel())
                            ->visible(fn ($record) => $record->status != GroupStatus::Confirmed),
                        Badge::make('progress')
                            ->color(fn ($record) => $record->progress->getColor())
                            ->label(fn ($record) => $record->progress->getLabel())
                            ->visible(fn ($record) => $record->status === GroupStatus::Confirmed && $record->progress != GroupProgress::Waiting),
                    ]),
                Tables\Columns\TextColumn::make('bill_items_count')
                    ->label('Purchases')
                    ->badge()
                    ->color(fn ($state) => $state > 0 ? 'success' : 'danger')
                    ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
                Tables\Columns\TextColumn::make('services')
                    ->label('Services')
                    ->badge()
                    ->getStateUsing(fn ($record) => $record->getServices())
                    ->color('primary')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('muassasah.company_name')
                    ->label('Muassasah')
                    ->description(fn ($record) => $record->number)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('arrival_date')
                    ->label('Arrival')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('departure_date')
                    ->label('Departure')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date Created')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by.name')
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->visible(auth()->user()->can('view-userstamps')),
                Tables\Columns\TextColumn::make('updated_by.name')
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->visible(auth()->user()->can('view-userstamps')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(GroupStatus::class),
                Tables\Filters\SelectFilter::make('customer')
                    ->relationship('customer', 'name')
                    ->searchable(),
                Tables\Filters\SelectFilter::make('services')
                    ->options(Group::GROUP_SERVICES)
                    ->multiple()
                    ->query(function ($query, $data) {
                        if (filled($data['values'])) {
                            $query->where(function ($query) use ($data) {
                                collect($data['values'])
                                    ->each(function ($value) use ($query) {
                                        $query
                                            ->where('services', 'like', '%' . $value . '%');
                                    });
                            });
                        }

                        return $query;
                    }),
                Tables\Filters\SelectFilter::make('muassasah')
                    ->label('Muassasah')
                    ->relationship('muassasah', 'company_name')
                    ->searchable(),
                DateRangeFilter::make('arrival_date')
                    ->indicateUsing(function ($data) {
                        $datesString = data_get($data, 'arrival_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Arrival date: {$datesString}";
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('confirm')
                    ->visible(fn ($record) => auth()->user()->hasRole(['Admin', 'Operator', 'Finance']) && $record->status == GroupStatus::Draft)
                    ->color('success')
                    ->icon('heroicon-m-check')
                    ->action(function (Group $record) {
                        $record->update(['status' => GroupStatus::Confirmed]);
                    })
                    ->requiresConfirmation(),
                Tables\Actions\ActionGroup::make([
                    Group::tableActionUpdateProgress(),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('duplicate')
                            ->visible(fn () => auth()->user()->hasRole(['Admin', 'Admin Operator']))
                            ->color('gray')
                            ->icon('heroicon-m-square-2-stack')
                            ->action(function (Group $record) {
                                $dup = $record->duplicate();

                                return redirect(static::getUrl('edit', ['record' => $dup]));
                            }),
                        Tables\Actions\ViewAction::make(),
                        Tables\Actions\EditAction::make(),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('cancel')
                            ->icon('heroicon-m-x-mark')
                            ->color('danger')
                            ->requiresConfirmation()
                            ->action(function ($record) {
                                $record->update([
                                    'status' => GroupStatus::Cancelled,
                                ]);
                            })
                            ->visible(fn ($record) => $record->status !== GroupStatus::Cancelled && auth('web')->user()->hasRole(['Admin'])),
                        Tables\Actions\DeleteAction::make(),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->bulkActions([])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationGroup::make('Hotels & Flights', [
                RelationManagers\GroupHotelsRelationManager::class,
                RelationManagers\FlightsRelationManager::class,
            ]),
            RelationManagers\ItinerariesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGroups::route('/'),
            'create' => Pages\CreateGroup::route('/create'),
            'view' => Pages\ViewGroup::route('/{record}'),
            'edit' => Pages\EditGroup::route('/{record}/edit'),
            'manifest' => Pages\ManageGroupManifest::route('/{record}/manifest'),
            'roomlist' => Pages\Roomlist::route('/{record}/roomlist'),
            'activites' => Pages\ViewGroupActivities::route('/{record}/activities'),
            'pilgrim-details' => Pages\PilgrimDetails::route('/{record}/pilgrim-details/{pilgrim}'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewGroup::class,
            Pages\EditGroup::class,
            Pages\ManageGroupManifest::class,
            Pages\Roomlist::class,
            Pages\ViewGroupActivities::class,
        ]);
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\ManifestOverview::class,
        ];
    }
}
