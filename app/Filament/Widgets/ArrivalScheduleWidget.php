<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\GroupResource;
use App\Models\GroupFlight;
use App\Models\User;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Closure;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ArrivalScheduleWidget extends BaseWidget
{
    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    public static function canView(): bool
    {
        $user = auth()->user();

        return $user->can('widget.arrivals') || $user->hasRole(['Admin', 'Operator', 'Airport Handler', 'Mutawif', 'Customer']);
    }

    protected function getTableQuery(): Builder
    {
        /** @var User */
        $user = auth()->user();

        return GroupFlight::query()
            ->with([
                'airline',
                'group.muassasah',
                'group.customer',
                'group.group_hotels.hotel',
            ])
            ->where('type', 'arrival')
            ->when($user->hasExactRoles('Airport Handler'), function ($query) use ($user) {
                $userVendorIds = $user->vendors()->pluck('id');
                $airportCodes = DB::table('airport_handler')
                    ->whereIn('handler_id', $userVendorIds)
                    ->pluck('airport_code');

                if (filled($airportCodes)) {
                    $query->whereIn('to', $airportCodes);
                }

                if (count($userVendorIds) > 0) {
                    $query->whereIn('handler_id', $userVendorIds);
                }
            })
            ->when($user->hasExactRoles('Mutawif'), function ($query) use ($user) {
                $query->whereHas('group', function ($query) use ($user) {
                    $query->where('mutawif_id', $user->id)
                        ->orWhere('mutawif_2_id', $user->id)
                        ->orWhere('mutawif_3_id', $user->id);
                });
            })
            ->when($user->hasExactRoles('Customer'), function ($query) use ($user) {
                $query->whereHas('group', function ($query) use ($user) {
                    $query->whereHas('customer', fn ($query) => $query->where('user_id', $user->id));
                });
            })
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->whereDate('date_eta', '>=', today());
    }

    protected function getTableRecordUrlUsing(): ?Closure
    {
        return function (GroupFlight $groupFlight) {
            return GroupResource::getUrl('view', ['record' => $groupFlight->group_id]);
        };
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('date_etd')
                ->date()
                ->sortable()
                ->label('Date'),
            Tables\Columns\TextColumn::make('to')
                ->label('Location'),
            BadgeableColumn::make('flight_number')
                ->label('Flight')
                ->description(fn ($record) => $record->airline?->name ?? null)
                ->suffixBadges([
                    Badge::make('pax')
                        ->label(fn ($record) => $record->total_pax . ' pax'),
                ])
                ->searchable(true, fn ($query, $search) => $query
                    ->orWhere('flight_number', 'like', "%{$search}%")
                    ->orWhereHas('airline', fn ($q) => $q->where('name', 'like', "%{$search}%"))),
            Tables\Columns\TextColumn::make('etd')
                ->label('ETD'),
            Tables\Columns\TextColumn::make('eta')
                ->label('ETA'),
            Tables\Columns\TextColumn::make('group')
                ->getStateUsing(fn ($record) => $record->group->customer?->name ?? '-')
                ->description(fn ($record) => $record->group->name)
                ->searchable(true, fn ($query, $search) => $query
                    ->orWhereHas('group', fn ($q) => $q->where('name', 'like', "%{$search}%"))
                    ->orWhereHas('group', fn ($q) => $q->whereHas('customer', fn ($q) => $q->where('name', 'like', "%{$search}%"))))
                ->label('Group'),
            Tables\Columns\TextColumn::make('muassasah')
                ->label('Muassasah')
                ->getStateUsing(fn ($record) => $record->group->muassasah?->company_name)
                ->description(fn ($record) => $record->group->number)
                ->toggleable(),
            Tables\Columns\TextColumn::make('services')
                ->label('Services')
                ->badge()
                ->getStateUsing(fn ($record) => $record->group->getServices())
                ->toggleable(),
            Tables\Columns\TextColumn::make('group.group_hotels')
                ->label('Hotels')
                ->badge()
                ->toggleable()
                ->getStateUsing(fn ($record) => in_array('hotel', $record->group->services ?? []) || in_array('hotel_service', $record->group->services ?? [])
                        ? $record->group->group_hotels
                        : null)
                ->formatStateUsing(fn ($state) => $state->hotel->fullname)
                ->color(fn ($state) => $state->is_confirmed ? 'success' : 'danger')
                ->icon(fn ($state) => $state->is_confirmed ? 'heroicon-m-check' : 'heroicon-m-x-mark')
                ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Finance'])),
        ];
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'date_eta';
    }

    public function getDefaultTableRecordsPerPageSelectOption(): int
    {
        return 5;
    }

    protected function getTableRecordsPerPageSelectOptions(): ?array
    {
        return [5, 10, 25, 50];
    }
}
