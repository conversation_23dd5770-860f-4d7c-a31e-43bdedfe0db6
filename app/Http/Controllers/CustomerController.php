<?php

namespace App\Http\Controllers;

use App\Enums\InvoiceStatus;
use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;

class CustomerController extends Controller
{
    public function statements(Customer $customerUuid)
    {
        $customer = $customerUuid;

        // Get all invoices for the customer
        $invoices = Invoice::where('customer_id', $customer->id)
            ->with(['group', 'items', 'payments'])
            ->orderBy('invoice_date', 'desc')
            ->get();

        // Get all payments for the customer's invoices
        $payments = InvoicePayment::query()
            ->with(['invoice'])
            ->whereIn('invoice_id', $invoices->pluck('id'))
            ->orderBy('paid_at', 'desc')
            ->get();

        // Calculate total invoice amount, total paid, and balance
        $totalInvoice = $invoices->sum(fn ($inv) => $inv->total * $inv->exchange_rate);
        $totalPaid = $invoices->sum(fn ($inv) => $inv->paid * $inv->exchange_rate);
        $balance = $totalInvoice - $totalPaid;

        $overdueBalance = $invoices->where('status', InvoiceStatus::Overdue)->sum(function ($inv) {
            return ($inv->total - $inv->paid) * $inv->exchange_rate;
        });

        // Get deposit balance
        $depositBalance = $customer->deposit_balance;

        return view('customers.statements', compact(
            'customer',
            'invoices',
            'payments',
            'totalInvoice',
            'totalPaid',
            'balance',
            'depositBalance',
            'overdueBalance'
        ));
    }

    public function invoice(Customer $customerUuid, Invoice $invoice)
    {
        $customer = $customerUuid;

        abort_unless($customer->id == $invoice->customer->id, 404);

        return view('customers.invoice', compact('customer', 'invoice'));
    }
}
