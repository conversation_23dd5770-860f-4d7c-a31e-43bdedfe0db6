<?php

namespace App\Exports\Finance;

use App\Enums\InvoiceStatus;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class InvoiceReportExport implements WithMultipleSheets
{
    use Exportable;

    protected string $fileName = 'Invoice Report';

    public function __construct(private array $data, private array $period)
    {
        $this->fileName = 'invoice-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.xlsx';
    }

    public function sheets(): array
    {
        return [
            new InvoiceListSheet($this->data, $this->period),
            new InvoiceSummarySheet($this->data, $this->period),
            new AgingAnalysisSheet($this->data, $this->period),
        ];
    }
}

class InvoiceListSheet implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    use Exportable;

    public function __construct(private array $data, private array $period) {}

    public function title(): string
    {
        return 'Invoice List';
    }

    public function collection()
    {
        return new Collection($this->data['invoice_list']);
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'D' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'H' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'I' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'J' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'M' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'N' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'O' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function headings(): array
    {
        return [
            'Invoice ID',
            'Invoice Number',
            'Invoice Date',
            'Due Date',
            'Customer',
            'Group',
            'Subject',
            'Amount',
            'Paid',
            'Outstanding',
            'Status',
            'Currency',
            'Amount (IDR)',
            'Paid (IDR)',
            'Outstanding (IDR)',
            'Days Overdue',
        ];
    }

    public function map($invoice): array
    {
        return [
            $invoice['id'],
            $invoice['invoice_number'],
            Date::dateTimeToExcel($invoice['invoice_date']),
            Date::dateTimeToExcel($invoice['due_date']),
            $invoice['customer_name'],
            $invoice['group_name'],
            $invoice['subject'],
            $invoice['amount'],
            $invoice['paid'],
            $invoice['outstanding'],
            $invoice['status'] instanceof InvoiceStatus ? $invoice['status']->getLabel() : $invoice['status'],
            $invoice['currency_code'],
            $invoice['amount_idr'],
            $invoice['paid_idr'],
            $invoice['outstanding_idr'],
            $invoice['days_overdue'],
        ];
    }
}

class InvoiceSummarySheet implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    use Exportable;

    public function __construct(private array $data, private array $period) {}

    public function title(): string
    {
        return 'Summary by ' . ucfirst($this->data['group_by']);
    }

    public function collection()
    {
        $collection = new Collection;

        // Add summary metrics first
        $collection->push((object) [
            'type' => 'summary',
            'label' => 'Total Invoices',
            'count' => $this->data['summary']['total_invoices'],
            'amount' => null,
            'paid' => null,
            'outstanding' => null,
        ]);

        $collection->push((object) [
            'type' => 'summary',
            'label' => 'Total Amount',
            'count' => null,
            'amount' => $this->data['summary']['total_amount'],
            'paid' => null,
            'outstanding' => null,
        ]);

        $collection->push((object) [
            'type' => 'summary',
            'label' => 'Total Paid',
            'count' => null,
            'amount' => null,
            'paid' => $this->data['summary']['total_paid'],
            'outstanding' => null,
        ]);

        $collection->push((object) [
            'type' => 'summary',
            'label' => 'Total Outstanding',
            'count' => null,
            'amount' => null,
            'paid' => null,
            'outstanding' => $this->data['summary']['total_outstanding'],
        ]);

        $collection->push((object) [
            'type' => 'summary',
            'label' => 'Payment Rate (%)',
            'count' => round($this->data['summary']['payment_rate'], 2),
            'amount' => null,
            'paid' => null,
            'outstanding' => null,
        ]);

        // Add empty row
        $collection->push((object) [
            'type' => 'separator',
            'label' => '',
            'count' => null,
            'amount' => null,
            'paid' => null,
            'outstanding' => null,
        ]);

        // Add grouped data
        foreach ($this->data['grouped_data'] as $row) {
            $collection->push((object) [
                'type' => 'group',
                'label' => $row['label'],
                'count' => $row['count'],
                'amount' => $row['amount'],
                'paid' => $row['paid'],
                'outstanding' => $row['outstanding'],
            ]);
        }

        return $collection;
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function headings(): array
    {
        return [
            'Type',
            ucfirst($this->data['group_by']),
            'Count',
            'Amount',
            'Paid',
            'Outstanding',
        ];
    }

    public function map($row): array
    {
        return [
            $row->type,
            $row->label,
            $row->count,
            $row->amount,
            $row->paid,
            $row->outstanding,
        ];
    }
}

class AgingAnalysisSheet implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    use Exportable;

    public function __construct(private array $data, private array $period) {}

    public function title(): string
    {
        return 'Aging Analysis';
    }

    public function collection()
    {
        $aging = $this->data['aging_analysis'];

        return new Collection([
            (object) [
                'period' => 'Current (0-30 days)',
                'amount' => $aging['current'],
            ],
            (object) [
                'period' => '31-60 days',
                'amount' => $aging['days_31_60'],
            ],
            (object) [
                'period' => '61-90 days',
                'amount' => $aging['days_61_90'],
            ],
            (object) [
                'period' => 'Over 90 days',
                'amount' => $aging['over_90'],
            ],
            (object) [
                'period' => 'Total Outstanding',
                'amount' => array_sum($aging),
            ],
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function headings(): array
    {
        return [
            'Aging Period',
            'Outstanding Amount',
        ];
    }

    public function map($row): array
    {
        return [
            $row->period,
            $row->amount,
        ];
    }
}
