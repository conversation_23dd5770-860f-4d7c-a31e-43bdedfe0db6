<?php

namespace App\Exports\Finance;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BalanceSheetExport implements WithMultipleSheets
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'balance-sheet-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function sheets(): array
    {
        return [
            new BalanceSheetSummarySheet($this->data, $this->period),
            new BalanceSheetAssetsSheet($this->data, $this->period),
            new BalanceSheetLiabilitiesSheet($this->data, $this->period),
            new BalanceSheetEquitySheet($this->data, $this->period),
        ];
    }
}

class BalanceSheetSummarySheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        // Calculate totals
        $totalAssets = 0;
        foreach ($this->data['assets'] as $category => $accounts) {
            $categoryTotal = collect($accounts)->sum('amount');
            $totalAssets += $categoryTotal;
            $collection->push((object) [
                'section' => 'ASSETS',
                'category' => $category,
                'amount' => $categoryTotal,
            ]);
        }

        // Add total assets row
        $collection->push((object) [
            'section' => 'TOTAL ASSETS',
            'category' => '',
            'amount' => $totalAssets,
        ]);

        // Add empty row
        $collection->push((object) [
            'section' => '',
            'category' => '',
            'amount' => null,
        ]);

        // Calculate liabilities
        $totalLiabilities = 0;
        foreach ($this->data['liabilities'] as $category => $accounts) {
            $categoryTotal = collect($accounts)->sum('amount');
            $totalLiabilities += $categoryTotal;
            $collection->push((object) [
                'section' => 'LIABILITIES',
                'category' => $category,
                'amount' => $categoryTotal,
            ]);
        }

        // Add total liabilities row
        $collection->push((object) [
            'section' => 'TOTAL LIABILITIES',
            'category' => '',
            'amount' => $totalLiabilities,
        ]);

        // Add empty row
        $collection->push((object) [
            'section' => '',
            'category' => '',
            'amount' => null,
        ]);

        // Calculate equity
        $totalEquity = 0;
        foreach ($this->data['equity'] as $category => $accounts) {
            $categoryTotal = collect($accounts)->sum('amount');
            $totalEquity += $categoryTotal;
            $collection->push((object) [
                'section' => 'EQUITY',
                'category' => $category,
                'amount' => $categoryTotal,
            ]);
        }

        // Add total equity row
        $collection->push((object) [
            'section' => 'TOTAL EQUITY',
            'category' => '',
            'amount' => $totalEquity,
        ]);

        // Add empty row
        $collection->push((object) [
            'section' => '',
            'category' => '',
            'amount' => null,
        ]);

        // Add total liabilities and equity
        $collection->push((object) [
            'section' => 'TOTAL LIABILITIES & EQUITY',
            'category' => '',
            'amount' => $totalLiabilities + $totalEquity,
        ]);

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Section',
            'Category',
            'Amount (' . $this->data['currency_code'] . ')',
        ];
    }

    public function map($row): array
    {
        return [
            $row->section,
            $row->category,
            $row->amount !== null ? $row->amount * $this->data['exchange_rate'] : null,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class BalanceSheetAssetsSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['assets'] as $category => $accounts) {
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'code' => $account['code'] ?? '',
                    'name' => $account['name'],
                    'category' => $category,
                    'amount' => $account['amount'],
                ]);
            }
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Account Code',
            'Account Name',
            'Category',
            'Amount (' . $this->data['currency_code'] . ')',
        ];
    }

    public function map($row): array
    {
        return [
            $row->code,
            $row->name,
            $row->category,
            $row->amount * $this->data['exchange_rate'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Assets';
    }
}

class BalanceSheetLiabilitiesSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['liabilities'] as $category => $accounts) {
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'code' => $account['code'] ?? '',
                    'name' => $account['name'],
                    'category' => $category,
                    'amount' => $account['amount'],
                ]);
            }
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Account Code',
            'Account Name',
            'Category',
            'Amount (' . $this->data['currency_code'] . ')',
        ];
    }

    public function map($row): array
    {
        return [
            $row->code,
            $row->name,
            $row->category,
            $row->amount * $this->data['exchange_rate'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Liabilities';
    }
}

class BalanceSheetEquitySheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['equity'] as $category => $accounts) {
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'code' => $account['code'] ?? '',
                    'name' => $account['name'],
                    'category' => $category,
                    'amount' => $account['amount'],
                ]);
            }
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Account Code',
            'Account Name',
            'Category',
            'Amount (' . $this->data['currency_code'] . ')',
        ];
    }

    public function map($row): array
    {
        return [
            $row->code,
            $row->name,
            $row->category,
            $row->amount * $this->data['exchange_rate'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Equity';
    }
}
