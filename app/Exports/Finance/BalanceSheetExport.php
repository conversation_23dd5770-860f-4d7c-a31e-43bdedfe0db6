<?php

namespace App\Exports\Finance;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BalanceSheetExport implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'balance-sheet-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function collection()
    {
        $collection = collect();

        // ASSETS SECTION
        $collection->push((object) [
            'section' => 'ASSETS',
            'code' => '',
            'name' => '',
            'amount' => null,
        ]);

        $totalAssets = 0;
        foreach ($this->data['assets'] as $category => $accounts) {
            // Add category header
            $collection->push((object) [
                'section' => $category,
                'code' => '',
                'name' => '',
                'amount' => null,
            ]);

            // Add accounts in this category
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'section' => '',
                    'code' => $account['code'] ?? '',
                    'name' => $account['name'],
                    'amount' => $account['amount'],
                ]);
                $totalAssets += $account['amount'];
            }
        }

        // Add total assets
        $collection->push((object) [
            'section' => 'TOTAL ASSETS',
            'code' => '',
            'name' => '',
            'amount' => $totalAssets,
        ]);

        // Add empty row
        $collection->push((object) [
            'section' => '',
            'code' => '',
            'name' => '',
            'amount' => null,
        ]);

        // LIABILITIES SECTION
        $collection->push((object) [
            'section' => 'LIABILITIES',
            'code' => '',
            'name' => '',
            'amount' => null,
        ]);

        $totalLiabilities = 0;
        foreach ($this->data['liabilities'] as $category => $accounts) {
            // Add category header
            $collection->push((object) [
                'section' => $category,
                'code' => '',
                'name' => '',
                'amount' => null,
            ]);

            // Add accounts in this category
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'section' => '',
                    'code' => $account['code'] ?? '',
                    'name' => $account['name'],
                    'amount' => $account['amount'],
                ]);
                $totalLiabilities += $account['amount'];
            }
        }

        // Add total liabilities
        $collection->push((object) [
            'section' => 'TOTAL LIABILITIES',
            'code' => '',
            'name' => '',
            'amount' => $totalLiabilities,
        ]);

        // Add empty row
        $collection->push((object) [
            'section' => '',
            'code' => '',
            'name' => '',
            'amount' => null,
        ]);

        // EQUITY SECTION
        $collection->push((object) [
            'section' => 'EQUITY',
            'code' => '',
            'name' => '',
            'amount' => null,
        ]);

        $totalEquity = 0;
        foreach ($this->data['equity'] as $category => $accounts) {
            // Add category header
            $collection->push((object) [
                'section' => $category,
                'code' => '',
                'name' => '',
                'amount' => null,
            ]);

            // Add accounts in this category
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'section' => '',
                    'code' => $account['code'] ?? '',
                    'name' => $account['name'],
                    'amount' => $account['amount'],
                ]);
                $totalEquity += $account['amount'];
            }
        }

        // Add total equity
        $collection->push((object) [
            'section' => 'TOTAL EQUITY',
            'code' => '',
            'name' => '',
            'amount' => $totalEquity,
        ]);

        // Add empty row
        $collection->push((object) [
            'section' => '',
            'code' => '',
            'name' => '',
            'amount' => null,
        ]);

        // Add total liabilities and equity
        $collection->push((object) [
            'section' => 'TOTAL LIABILITIES & EQUITY',
            'code' => '',
            'name' => '',
            'amount' => $totalLiabilities + $totalEquity,
        ]);

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Section',
            'Account Code',
            'Account Name',
            'Amount (' . $this->data['currency_code'] . ')',
        ];
    }

    public function map($row): array
    {
        return [
            $row->section,
            $row->code,
            $row->name,
            $row->amount !== null ? $row->amount * $this->data['exchange_rate'] : null,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }
}
