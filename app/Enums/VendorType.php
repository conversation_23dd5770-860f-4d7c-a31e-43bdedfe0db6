<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum VendorType: string implements HasLabel
{
    case Ticketing = 'ticketing';
    case Muassasah = 'muassasah';
    case Hotel = 'hotel';
    case AirportHandler = 'airport-handler';
    case Catering = 'catering';
    case Transportation = 'transportation';
    case LuggageEquipment = 'luggage-equipment';
    case Snack = 'snack';
    case Fee = 'fee';
    case Siskopatuh = 'siskopatuh';
    case Others = 'others';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Ticketing => 'Ticketing',
            self::Muassasah => 'Visa (Muassasah)',
            self::Hotel => 'Hotel Broker',
            self::AirportHandler => 'Airport Handler',
            self::Catering => 'Catering',
            self::Transportation => 'Transportation',
            self::LuggageEquipment => 'Luggage and Equipment',
            self::Snack => 'Snack',
            self::Fee => 'Fee',
            self::Siskopatuh => 'Siskopatuh',
            self::Others => 'Others',
        };
    }
}
