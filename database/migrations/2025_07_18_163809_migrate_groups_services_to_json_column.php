<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Step 1: Add the services_json column
        Schema::table('groups', function (Blueprint $table) {
            $table->json('services_json')->nullable()->after('services');
        });

        // Step 2: Create the MariaDB JSON conversion function
        DB::unprepared('
            DROP FUNCTION IF EXISTS split_to_json_array;
        ');

        DB::unprepared("
            CREATE FUNCTION split_to_json_array(input TEXT)
            RETURNS TEXT
            DETERMINISTIC
            BEGIN
                DECLARE comma_idx INT;
                DECLARE val TEXT;
                DECLARE result TEXT DEFAULT '[';

                WHILE input IS NOT NULL AND input != '' DO
                    SET comma_idx = LOCATE(',', input);

                    IF comma_idx = 0 THEN
                        SET val = TRIM(input);
                        SET result = CONCAT(result, '\"', REPLACE(val, '\"', '\\\"'), '\"');
                        SET input = NULL;
                    ELSE
                        SET val = TRIM(SUBSTRING(input, 1, comma_idx - 1));
                        SET result = CONCAT(result, '\"', REPLACE(val, '\"', '\\\"'), '\",');
                        SET input = SUBSTRING(input, comma_idx + 1);
                    END IF;
                END WHILE;

                IF RIGHT(result, 1) = ',' THEN
                    SET result = LEFT(result, LENGTH(result) - 1);
                END IF;

                SET result = CONCAT(result, ']');
                RETURN result;
            END;
        ");

        // Step 3: Run the conversion
        DB::statement('
            UPDATE groups
            SET services_json = split_to_json_array(services)
        ');

        // Step 4: Remove the old services column
        Schema::table('groups', function (Blueprint $table) {
            $table->dropColumn('services');
        });

        // Step 5: Rename services_json to services
        Schema::table('groups', function (Blueprint $table) {
            $table->renameColumn('services_json', 'services');
        });

        // Step 6: Drop the function
        DB::unprepared('DROP FUNCTION IF EXISTS split_to_json_array;');
    }

    public function down(): void
    {
        //
    }
};
