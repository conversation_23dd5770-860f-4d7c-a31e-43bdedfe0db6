<?php

use App\Models\Finance\ExchangeRate;
use App\Models\Finance\JournalEntryItem;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('journal_entry_items', function (Blueprint $table) {
            $table->char('currency_code')->default('SAR')->after('type');
            $table->double('exchange_rate')->default(4300)->after('currency_code');
        });

        DB::table('journal_entry_items')
            ->leftJoin('journal_entries', 'journal_entry_items.entry_id', '=', 'journal_entries.id')
            ->select([
                DB::raw('DATE(journal_entries.entry_date) as entry_date'),
                'journal_entry_items.id as item_id',
            ])
            ->where('journal_entries.entry_date', '>=', now()->subDays(30))
            ->get()
            ->groupBy('entry_date')
            ->each(function ($group, $date) {
                $rate = ExchangeRate::getExchangeRate($date, 'IDR', 'SAR');
                $ids = $group->pluck('item_id')->toArray();

                JournalEntryItem::whereIn('id', $ids)
                    ->update(['exchange_rate' => $rate]);
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('journal_entry_items', function (Blueprint $table) {
            //
        });
    }
};
