<?php

use App\Models\Finance\CashTransaction;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cash_transactions', function (Blueprint $table) {
            $table->json('attachments')->nullable()->after('attachment');
        });

        CashTransaction::query()
            ->whereNotNull('attachment')
            ->get()
            ->each(function ($model) {
                $model->update([
                    'attachments' => [$model->attachment],
                ]);
            });

        Schema::table('cash_transactions', function (Blueprint $table) {
            $table->dropColumn('attachment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cash_transactions', function (Blueprint $table) {
            $table->string('attachment')->nullable()->after('attachments');
        });

        CashTransaction::query()
            ->whereNotNull('attachments')
            ->get()
            ->each(function ($bill) {
                $bill->update([
                    'attachment' => $bill->attachments[0] ?? null,
                ]);
            });

        Schema::table('cash_transactions', function (Blueprint $table) {
            $table->dropColumn('attachments');
        });
    }
};
