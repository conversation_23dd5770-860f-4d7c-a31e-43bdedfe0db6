<?php

use App\Models\Bill;
use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('debit_notes', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Vendor::class)->constrained()->cascadeOnDelete();

            $table->date('bill_date');
            $table->foreignIdFor(Bill::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Bill::class, 'used_for_bill_id')->nullable()->constrained()->nullOnDelete();

            $table->decimal('amount', 15)->default(0);
            $table->char('currency_code', 3)->default('SAR');
            $table->double('exchange_rate')->default(1);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('debit_notes');
    }
};
