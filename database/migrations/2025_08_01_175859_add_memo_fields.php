<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimates', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('invoices', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('invoice_payments', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('invoice_refunds', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('customer_cashes', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('bills', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('bill_payments', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('cash_transactions', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('user_cashes', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
        Schema::table('journal_entries', function (Blueprint $table) {
            $table->text('memo')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            //
        });
    }
};
