<?php

use App\Models\Finance\UserCash;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_cashes', function (Blueprint $table) {
            $table->json('attachments')->nullable()->after('attachment');
        });

        UserCash::query()
            ->whereNotNull('attachment')
            ->get()
            ->each(function ($model) {
                $model->updateQuietly([
                    'attachments' => [$model->attachment],
                ]);
            });

        Schema::table('user_cashes', function (Blueprint $table) {
            $table->dropColumn('attachment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_cashes', function (Blueprint $table) {
            $table->string('attachment')->nullable()->after('attachments');
        });

        UserCash::query()
            ->whereNotNull('attachments')
            ->get()
            ->each(function ($bill) {
                $bill->updateQuietly([
                    'attachment' => $bill->attachments[0] ?? null,
                ]);
            });

        Schema::table('user_cashes', function (Blueprint $table) {
            $table->dropColumn('attachments');
        });
    }
};
