<?php

use App\Models\Customer;
use App\Models\Finance\Invoice;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_notes', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Customer::class)->constrained()->cascadeOnDelete();

            $table->date('invoice_date');
            $table->foreignIdFor(Invoice::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Invoice::class, 'used_for_invoice_id')->nullable()->constrained()->nullOnDelete();

            $table->decimal('amount', 15)->default(0);
            $table->char('currency_code', 3)->default('SAR');
            $table->double('exchange_rate')->default(1);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_notes');
    }
};
