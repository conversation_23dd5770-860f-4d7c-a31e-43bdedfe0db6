<?php

use App\Enums\VendorType;
use App\Models\Group;
use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('groups', function (Blueprint $table) {
            $table->unsignedBigInteger('muassasah_id')->nullable()->after('institution_contact_phone');
        });

        $idMapping = Vendor::query()
            ->where('vendor_type', VendorType::Muassasah)
            ->pluck('id', 'original_id');

        Group::query()
            ->whereNotNull('institution_id')
            ->chunk(100, function ($groups) use (&$idMapping) {
                foreach ($groups as $group) {
                    $institutionId = $group->institution_id;
                    if (! isset($idMapping[$institutionId])) {
                        $institution = \App\Models\Institution::find($institutionId);
                        if ($institution) {
                            $vendor = Vendor::create([
                                'original_id' => $institution->id,
                                'vendor_type' => VendorType::Muassasah,
                                'company_name' => $institution->name,
                                'contact_email' => $institution->email,
                                'contact_phone' => $institution->phone,
                            ]);
                            $idMapping[$institutionId] = $vendor->id;
                        } else {
                            continue; // skip if institution not found
                        }
                    }
                    $group->update(['muassasah_id' => $idMapping[$institutionId]]);
                }
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('groups', function (Blueprint $table) {
            //
        });
    }
};
