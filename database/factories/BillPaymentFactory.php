<?php

namespace Database\Factories;

use App\Models\Bill;
use App\Models\BillPayment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BillPayment>
 */
class BillPaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'bill_id' => Bill::factory(),
            'user_id' => User::factory(),
            'cash_account_id' => null, // Will be set automatically in the model's boot method
            'user_cash_id' => null,
            'paid_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'amount' => fake()->randomFloat(2, 100, 5000),
            'currency_code' => fake()->randomElement(['SAR', 'IDR']),
            'exchange_rate' => fake()->randomFloat(2, 0.5, 1.5),
            'description' => fake()->sentence(),
            'attachment' => null,
        ];
    }

    /**
     * Configure the factory to use an existing bill.
     *
     * @param  \App\Models\Bill|int  $bill
     * @return static
     */
    public function forBill($bill)
    {
        return $this->state(function (array $attributes) use ($bill) {
            $billId = $bill instanceof Bill ? $bill->id : $bill;
            $billModel = $bill instanceof Bill ? $bill : Bill::find($billId);

            return [
                'bill_id' => $billId,
                'currency_code' => $billModel ? $billModel->currency_code : $attributes['currency_code'],
                'exchange_rate' => $billModel ? $billModel->exchange_rate : $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to use an existing user.
     *
     * @param  \App\Models\User|int  $user
     * @return static
     */
    public function forUser($user)
    {
        return $this->state(function (array $attributes) use ($user) {
            $userId = $user instanceof User ? $user->id : $user;

            return [
                'user_id' => $userId,
            ];
        });
    }

    /**
     * Configure the factory to create a payment with a specific amount.
     *
     * @return static
     */
    public function amount(float $amount)
    {
        return $this->state(function (array $attributes) use ($amount) {
            return [
                'amount' => $amount,
            ];
        });
    }

    /**
     * Configure the factory to create a payment with a specific currency.
     *
     * @return static
     */
    public function currency(string $currencyCode, ?float $exchangeRate = null)
    {
        return $this->state(function (array $attributes) use ($currencyCode, $exchangeRate) {
            return [
                'currency_code' => $currencyCode,
                'exchange_rate' => $exchangeRate ?? $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to create a payment with a specific date.
     *
     * @param  \DateTime|\DateTimeImmutable|string  $date
     * @return static
     */
    public function paidAt($date)
    {
        return $this->state(function (array $attributes) use ($date) {
            return [
                'paid_at' => $date,
            ];
        });
    }

    /**
     * Configure the factory to create a payment for the full bill amount.
     *
     * @return static
     */
    public function fullPayment()
    {
        return $this->state(function (array $attributes) {
            $bill = Bill::find($attributes['bill_id']);

            if (! $bill) {
                return $attributes;
            }

            return [
                'amount' => $bill->total - $bill->paid,
            ];
        });
    }
}
