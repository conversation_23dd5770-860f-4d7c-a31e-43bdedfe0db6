<?php

namespace Database\Factories\Finance;

use App\Models\Bill;
use App\Models\Finance\DebitNote;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\DebitNote>
 */
class DebitNoteFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DebitNote::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'vendor_id' => Vendor::factory(),
            'bill_date' => fake()->dateTimeBetween('-3 months', 'now'),
            'bill_id' => Bill::factory(),
            'used_for_bill_id' => null,
            'amount' => fake()->randomFloat(2, 10, 1000),
            'currency_code' => fake()->randomElement(['SAR', 'IDR']),
            'exchange_rate' => fake()->randomFloat(2, 0.5, 1.5),
        ];
    }

    /**
     * Configure the factory to use an existing vendor.
     *
     * @param  \App\Models\Vendor|int  $vendor
     * @return static
     */
    public function forVendor($vendor)
    {
        return $this->state(function (array $attributes) use ($vendor) {
            $vendorId = $vendor instanceof Vendor ? $vendor->id : $vendor;

            return [
                'vendor_id' => $vendorId,
            ];
        });
    }

    /**
     * Configure the factory to use an existing bill.
     *
     * @param  \App\Models\Bill|int  $bill
     * @return static
     */
    public function forBill($bill)
    {
        return $this->state(function (array $attributes) use ($bill) {
            $billId = $bill instanceof Bill ? $bill->id : $bill;
            $billModel = $bill instanceof Bill ? $bill : Bill::find($billId);

            return [
                'bill_id' => $billId,
                'vendor_id' => $billModel ? $billModel->vendor_id : $attributes['vendor_id'],
                'bill_date' => $billModel ? $billModel->bill_date : $attributes['bill_date'],
                'currency_code' => $billModel ? $billModel->currency_code : $attributes['currency_code'],
                'exchange_rate' => $billModel ? $billModel->exchange_rate : $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to create a debit note with a specific amount.
     *
     * @return static
     */
    public function amount(float $amount)
    {
        return $this->state(function (array $attributes) use ($amount) {
            return [
                'amount' => $amount,
            ];
        });
    }

    /**
     * Configure the factory to create a debit note with a specific currency.
     *
     * @return static
     */
    public function currency(string $currencyCode, ?float $exchangeRate = null)
    {
        return $this->state(function (array $attributes) use ($currencyCode, $exchangeRate) {
            return [
                'currency_code' => $currencyCode,
                'exchange_rate' => $exchangeRate ?? $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to mark the debit note as used for a specific bill.
     *
     * @param  \App\Models\Bill|int  $bill
     * @return static
     */
    public function usedForBill($bill)
    {
        return $this->state(function (array $attributes) use ($bill) {
            $billId = $bill instanceof Bill ? $bill->id : $bill;

            return [
                'used_for_bill_id' => $billId,
            ];
        });
    }
}
