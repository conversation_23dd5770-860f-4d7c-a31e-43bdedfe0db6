<?php

namespace Database\Factories\Finance;

use App\Enums\InvoiceStatus;
use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\Group;
use App\Models\LA\Package;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\Invoice>
 */
class InvoiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Invoice::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'invoice_date' => fake()->dateTimeBetween('-3 months', 'now'),
            'invoice_number' => 'INV-' . date('Y') . fake()->unique()->randomNumber(4, true),
            'customer_id' => Customer::factory(),
            'group_id' => Group::factory(),
            'package_id' => null, // Package::factory(),
            'due_date' => fake()->dateTimeBetween('now', '+30 days'),
            'currency_code' => fake()->randomElement(['SAR', 'IDR']),
            'exchange_rate' => fake()->randomFloat(2, 0.5, 1.5),
            'subject' => fake()->sentence(),
            'notes' => fake()->paragraph(),
            'terms' => Invoice::getDefaultTerms(),
            'total' => 0,
            'paid' => 0,
            'status' => InvoiceStatus::Unpaid,
        ];
    }

    /**
     * Indicate that the invoice is paid.
     *
     * @return static
     */
    public function paid()
    {
        return $this->state(function (array $attributes) {
            return [
                'paid' => $attributes['total'],
                'status' => InvoiceStatus::Paid,
            ];
        });
    }

    /**
     * Indicate that the invoice is partially paid.
     *
     * @return static
     */
    public function partiallyPaid()
    {
        return $this->state(function (array $attributes) {
            $paid = $attributes['total'] * fake()->randomFloat(2, 0.1, 0.9);

            return [
                'paid' => $paid,
                'status' => InvoiceStatus::PaidPartial,
            ];
        });
    }

    /**
     * Indicate that the invoice is overdue.
     *
     * @return static
     */
    public function overdue()
    {
        return $this->state(function (array $attributes) {
            return [
                'due_date' => fake()->dateTimeBetween('-30 days', '-1 day'),
                'status' => InvoiceStatus::Overdue,
            ];
        });
    }

    /**
     * Indicate that the invoice is cancelled.
     *
     * @return static
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => InvoiceStatus::Cancelled,
                'cancellation_note' => fake()->sentence(),
            ];
        });
    }

    /**
     * Create an invoice with items.
     *
     * @return static
     */
    public function withItems(int $count = 3)
    {
        return $this->afterCreating(function (Invoice $invoice) use ($count) {
            \App\Models\Finance\InvoiceItem::factory()
                ->count($count)
                ->create(['invoice_id' => $invoice->id]);
        });
    }

    /**
     * Create an invoice with payments.
     *
     * @return static
     */
    public function withPayments(int $count = 1)
    {
        return $this->afterCreating(function (Invoice $invoice) use ($count) {
            \App\Models\Finance\InvoicePayment::factory()
                ->count($count)
                ->create(['invoice_id' => $invoice->id]);
        });
    }

    /**
     * Create an invoice with refunds.
     *
     * @return static
     */
    public function withRefunds(int $count = 1)
    {
        return $this->afterCreating(function (Invoice $invoice) use ($count) {
            \App\Models\Finance\InvoiceRefund::factory()
                ->count($count)
                ->create(['invoice_id' => $invoice->id]);
        });
    }
}
