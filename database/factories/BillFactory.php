<?php

namespace Database\Factories;

use App\Enums\InvoiceStatus;
use App\Models\Bill;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bill>
 */
class BillFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'bill_date' => fake()->dateTimeBetween('-3 months', 'now'),
            'bill_number' => 'BI-' . date('Y') . fake()->unique()->randomNumber(4, true),
            'vendor_id' => Vendor::factory(),
            'due_date' => fake()->dateTimeBetween('now', '+30 days'),
            'currency_code' => fake()->randomElement(['SAR', 'IDR']),
            'exchange_rate' => fake()->randomFloat(2, 0.5, 1.5),
            'subject' => fake()->sentence(),
            'notes' => fake()->paragraph(),
            'total' => 0,
            'paid' => 0,
            'status' => InvoiceStatus::Unpaid,
        ];
    }

    /**
     * Configure the factory to use an existing vendor.
     *
     * @param  \App\Models\Vendor|int  $vendor
     * @return static
     */
    public function forVendor($vendor)
    {
        return $this->state(function (array $attributes) use ($vendor) {
            $vendorId = $vendor instanceof Vendor ? $vendor->id : $vendor;

            return [
                'vendor_id' => $vendorId,
            ];
        });
    }

    /**
     * Configure the factory to create a bill with a specific total.
     *
     * @return static
     */
    public function total(float $total)
    {
        return $this->state(function (array $attributes) use ($total) {
            return [
                'total' => $total,
            ];
        });
    }

    /**
     * Configure the factory to create a bill with a specific currency.
     *
     * @return static
     */
    public function currency(string $currencyCode, ?float $exchangeRate = null)
    {
        return $this->state(function (array $attributes) use ($currencyCode, $exchangeRate) {
            return [
                'currency_code' => $currencyCode,
                'exchange_rate' => $exchangeRate ?? $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to create a paid bill.
     *
     * @return static
     */
    public function paid()
    {
        return $this->state(function (array $attributes) {
            return [
                'paid' => $attributes['total'],
                'status' => InvoiceStatus::Paid,
            ];
        });
    }

    /**
     * Configure the factory to create an unpaid bill.
     *
     * @return static
     */
    public function unpaid()
    {
        return $this->state(function (array $attributes) {
            return [
                'paid' => 0,
                'status' => InvoiceStatus::Unpaid,
            ];
        });
    }
}
