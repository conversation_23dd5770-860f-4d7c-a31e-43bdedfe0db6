<?php

namespace Database\Factories;

use App\Models\Bill;
use App\Models\BillItem;
use App\Models\Finance\Product;
use App\Models\Group;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BillItem>
 */
class BillItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = BillItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'bill_id' => Bill::factory(),
            'group_id' => null,
            'product_id' => null,
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(1),
            'quantity' => fake()->numberBetween(1, 10),
            'unit_price' => fake()->randomFloat(2, 10, 1000),
            'vat' => fake()->randomFloat(2, 0, 15),
            'order_column' => 0, // Will be automatically set by SortableTrait
        ];
    }

    /**
     * Configure the factory to use an existing bill.
     *
     * @param  \App\Models\Bill|int  $bill
     * @return static
     */
    public function forBill($bill)
    {
        return $this->state(function (array $attributes) use ($bill) {
            return [
                'bill_id' => $bill instanceof Bill ? $bill->id : $bill,
            ];
        });
    }

    /**
     * Configure the factory to use an existing group.
     *
     * @param  \App\Models\Group|int  $group
     * @return static
     */
    public function forGroup($group)
    {
        return $this->state(function (array $attributes) use ($group) {
            return [
                'group_id' => $group instanceof Group ? $group->id : $group,
            ];
        });
    }

    /**
     * Configure the factory to use an existing product.
     *
     * @param  \App\Models\Finance\Product|int  $product
     * @return static
     */
    public function forProduct($product)
    {
        return $this->state(function (array $attributes) use ($product) {
            $productId = $product instanceof Product ? $product->id : $product;
            $productModel = $product instanceof Product ? $product : Product::find($productId);

            return [
                'product_id' => $productId,
                'name' => $productModel ? $productModel->name : $attributes['name'],
                'description' => $productModel ? $productModel->description : $attributes['description'],
                'unit_price' => $productModel ? $productModel->cost : $attributes['unit_price'],
            ];
        });
    }

    /**
     * Configure the factory to create an item with a specific quantity.
     *
     * @return static
     */
    public function quantity(int $quantity)
    {
        return $this->state(function (array $attributes) use ($quantity) {
            return [
                'quantity' => $quantity,
            ];
        });
    }

    /**
     * Configure the factory to create an item with a specific unit price.
     *
     * @return static
     */
    public function unitPrice(float $unitPrice)
    {
        return $this->state(function (array $attributes) use ($unitPrice) {
            return [
                'unit_price' => $unitPrice,
            ];
        });
    }

    /**
     * Configure the factory to create an item with no VAT.
     *
     * @return static
     */
    public function noVat()
    {
        return $this->state(function (array $attributes) {
            return [
                'vat' => 0,
            ];
        });
    }
}
