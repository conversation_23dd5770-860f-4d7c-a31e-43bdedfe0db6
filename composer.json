{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "vcs", "url": "https://github.com/laravel-shift/filament-import.git"}, {"type": "vcs", "url": "**************:arrahmah/umrahservice-groups-module.git"}], "require": {"php": "^8.2", "afsakar/filament-otp-login": "^1.4", "akaunting/laravel-money": "^5.1", "ariaieboy/filament-currency": "^1.1", "arrahmah/umrahservice-groups-module": "dev-main", "awcodes/filament-badgeable-column": "^2.0", "awcodes/filament-quick-create": "^3.4", "awcodes/filament-table-repeater": "^3.1", "awcodes/shout": "^2.0", "bezhansalleh/filament-panel-switch": "^1.0.0", "codeat3/blade-phosphor-icons": "^2.0", "codewithdennis/filament-select-tree": "^3.1", "eightynine/filament-excel-import": "^3.1", "endroid/qr-code": "^5.0", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.0", "flowframe/laravel-trend": "^0.4.0", "geniusts/hijri-dates": "^1.1", "gotenberg/gotenberg-php": "^2.10", "guava/filament-clusters": "^1.1", "guzzlehttp/guzzle": "^7.2", "jaocero/activity-timeline": "^1.2", "jeffgreco13/filament-breezy": "^2.0", "kenepa/resource-lock": "^2.0", "kirschbaum-development/eloquent-power-joins": "^3.2", "konnco/filament-import": "dev-l11-compatibility", "laravel/framework": "^11.9", "laravel/octane": "^2.6", "laravel/pail": "^1.1", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^3.0", "lorisleiva/laravel-actions": "^2.7", "maatwebsite/excel": "^3.1", "malzariey/filament-daterangepicker-filter": "^3.2", "marvinosswald/filament-input-select-affix": "^0.2.0", "motekar/laravel-pdf": "^1.0", "motekar/laravel-zip": "^1.0", "phpoffice/phpword": "^1.0", "prism-php/prism": "^0.78.0", "pxlrbt/filament-environment-indicator": "^2.0", "resend/resend-php": "^0.18.0", "rmsramos/activitylog": "^1.0", "saade/filament-fullcalendar": "^3.0.0-beta", "saade/filament-laravel-log": "^3.2", "secondnetwork/blade-tabler-icons": "^3.26", "shuvroroy/filament-spatie-laravel-backup": "^2.0", "spatie/eloquent-sortable": "^4.0", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-login-link": "^1.3", "spatie/laravel-permission": "^6.0", "spatie/laravel-settings": "^3.2", "staudenmeir/belongs-to-through": "^2.16", "staudenmeir/laravel-adjacency-list": "^1.13", "stechstudio/filament-impersonate": "^3.9", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.23", "larastan/larastan": "^2.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.6", "pestphp/pest-plugin-laravel": "^3.0", "pestphp/pest-plugin-livewire": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan filament:upgrade"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": true}}}